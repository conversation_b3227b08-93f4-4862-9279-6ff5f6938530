{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "preprocessing_header",
   "metadata": {},
   "source": [
    "# Pré-processamento e Limpeza de Dados - Chilli Beans\n",
    "\n",
    "## Objetivo\n",
    "Realizar pré-processamento e limpeza dos dados seguindo rigorosamente as etapas definidas:\n",
    "\n",
    "1. **Tratamento de valores ausentes (missing values)**\n",
    "2. **Detecção e tratamento de outliers**\n",
    "3. **Transformações obrigatórias:**\n",
    "   - Normalização/Padronização (colunas numéricas)\n",
    "   - Codificação (colunas categóricas)\n",
    "4. **Documentação das ações realizadas**\n",
    "\n",
    "O resultado final será um dataset limpo e transformado, pronto para treino de modelos preditivos."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Importação das bibliotecas necessárias para pré-processamento\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from scipy import stats\n",
    "from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder\n",
    "from sklearn.impute import SimpleImputer\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Configurações para melhor visualização dos dados\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.max_colwidth', 50)"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step1_header",
   "metadata": {},
   "source": [
    "## 1. Carregamento e Análise Inicial dos Dados\n",
    "\n",
    "Carregamos os dados já filtrados pelo módulo `data_filtering.py` que aplica regras de negócio."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "load_data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Importar módulo de filtragem desenvolvido para aplicar regras de negócio\n",
    "import sys\n",
    "import os\n",
    "sys.path.append(os.path.dirname(os.path.abspath('.')))\n",
    "from data_filtering import apply_business_filters\n",
    "\n",
    "# Carregar dados com filtros de negócio já aplicados\n",
    "df = apply_business_filters('../assets/dados.csv', verbose=False)\n",
    "\n",
    "# Exibir informações básicas do dataset\n",
    "print(f\"Dataset carregado: {df.shape[0]:,} registros e {df.shape[1]} colunas\")\n",
    "print(f\"Período: {df['ID_Date'].min()} a {df['ID_Date'].max()}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step2_header",
   "metadata": {},
   "source": [
    "## 2. Tratamento de Valores Ausentes (Missing Values)\n",
    "\n",
    "Identificamos e tratamos valores nulos usando estratégias específicas para cada tipo de dados."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "missing_values_treatment",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Identificar colunas com valores ausentes\n",
    "missing_data = df.isnull().sum()\n",
    "missing_cols = missing_data[missing_data > 0]\n",
    "\n",
    "print(f\"Colunas com valores ausentes: {len(missing_cols)}\")\n",
    "print(f\"Total de valores ausentes: {missing_data.sum():,}\")\n",
    "\n",
    "# Criar cópia para tratamento\n",
    "df_treated = df.copy()\n",
    "acoes_missing = []\n",
    "\n",
    "# Aplicar estratégias de tratamento\n",
    "for coluna in missing_cols.index:\n",
    "    missing_count = missing_cols[coluna]\n",
    "    missing_percent = (missing_count / len(df)) * 100\n",
    "    \n",
    "    if missing_percent > 50:\n",
    "        # Remover colunas com mais de 50% de valores ausentes\n",
    "        df_treated = df_treated.drop(columns=[coluna])\n",
    "        acoes_missing.append(f\"{coluna}: Remoção da coluna (>50% ausentes)\")\n",
    "    elif df_treated[coluna].dtype == 'object':\n",
    "        # Para categóricas: imputar com moda\n",
    "        moda = df_treated[coluna].mode()[0] if not df_treated[coluna].mode().empty else 'Não informado'\n",
    "        df_treated[coluna].fillna(moda, inplace=True)\n",
    "        acoes_missing.append(f\"{coluna}: Imputação com moda '{moda}'\")\n",
    "    else:\n",
    "        # Para numéricas: imputar com mediana\n",
    "        mediana = df_treated[coluna].median()\n",
    "        df_treated[coluna].fillna(mediana, inplace=True)\n",
    "        acoes_missing.append(f\"{coluna}: Imputação com mediana {mediana:.2f}\")\n",
    "\n",
    "# Atualizar dataframe principal\n",
    "df = df_treated.copy()\n",
    "print(f\"\\nValores ausentes após tratamento: {df.isnull().sum().sum()}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step3_header",
   "metadata": {},
   "source": [
    "## 3. Detecção e Tratamento de Outliers\n",
    "\n",
    "Identificamos outliers usando método IQR e aplicamos estratégias de correção baseadas no percentual de outliers."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "outlier_treatment",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Função para detectar outliers usando IQR\n",
    "def detect_outliers_iqr(data, column):\n",
    "    Q1 = data[column].quantile(0.25)\n",
    "    Q3 = data[column].quantile(0.75)\n",
    "    IQR = Q3 - Q1\n",
    "    lower_bound = Q1 - 1.5 * IQR\n",
    "    upper_bound = Q3 + 1.5 * IQR\n",
    "    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n",
    "    return outliers, lower_bound, upper_bound\n",
    "\n",
    "# Colunas numéricas para análise de outliers\n",
    "colunas_outliers = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']\n",
    "colunas_outliers = [col for col in colunas_outliers if col in df.columns]\n",
    "\n",
    "df_outlier_treated = df.copy()\n",
    "acoes_outliers = []\n",
    "\n",
    "# Detectar e tratar outliers para cada coluna\n",
    "for coluna in colunas_outliers:\n",
    "    outliers, lower_bound, upper_bound = detect_outliers_iqr(df, coluna)\n",
    "    percentual_outliers = len(outliers) / len(df) * 100\n",
    "    \n",
    "    if percentual_outliers > 10:\n",
    "        # Cap truncation para >10% outliers\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] < lower_bound, lower_bound, df_outlier_treated[coluna]\n",
    "        )\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] > upper_bound, upper_bound, df_outlier_treated[coluna]\n",
    "        )\n",
    "        acoes_outliers.append(f\"{coluna}: Cap truncation ({percentual_outliers:.1f}% outliers)\")\n",
    "    elif percentual_outliers > 1:\n",
    "        # Remoção para 1-10% outliers\n",
    "        outliers_mask = (df_outlier_treated[coluna] < lower_bound) | (df_outlier_treated[coluna] > upper_bound)\n",
    "        df_outlier_treated = df_outlier_treated[~outliers_mask]\n",
    "        acoes_outliers.append(f\"{coluna}: Remoção de {outliers_mask.sum()} registros ({percentual_outliers:.1f}% outliers)\")\n",
    "    else:\n",
    "        # Manter outliers <1%\n",
    "        acoes_outliers.append(f\"{coluna}: Mantidos ({percentual_outliers:.1f}% outliers)\")\n",
    "\n",
    "# Atualizar dataframe principal\n",
    "df = df_outlier_treated.copy()\n",
    "print(f\"Registros após tratamento de outliers: {len(df):,}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step4_header",
   "metadata": {},
   "source": [
    "## 4. Normalização e Padronização\n",
    "\n",
    "Aplicamos normalização Min-Max e padronização Z-score nas colunas numéricas."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "normalization",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Colunas para normalização\n",
    "colunas_para_normalizar = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']\n",
    "colunas_para_normalizar = [col for col in colunas_para_normalizar if col in df.columns]\n",
    "\n",
    "df_normalized = df.copy()\n",
    "acoes_normalizacao = []\n",
    "\n",
    "# Aplicar Min-Max Normalization (0-1)\n",
    "scaler_minmax = MinMaxScaler()\n",
    "for coluna in colunas_para_normalizar:\n",
    "    # Aplicar normalização Min-Max\n",
    "    df_normalized[f'{coluna}_normalized'] = scaler_minmax.fit_transform(df[[coluna]])\n",
    "    acoes_normalizacao.append(f\"{coluna} → normalização Min-Max (0-1)\")\n",
    "\n",
    "# Aplicar Padronização Z-score (média=0, desvio=1)\n",
    "scaler_standard = StandardScaler()\n",
    "for coluna in colunas_para_normalizar:\n",
    "    # Aplicar padronização Z-score\n",
    "    df_normalized[f'{coluna}_standardized'] = scaler_standard.fit_transform(df[[coluna]])\n",
    "    acoes_normalizacao.append(f\"{coluna} → padronização Z-score (μ=0, σ=1)\")\n",
    "\n",
    "print(f\"Transformações de escala aplicadas em {len(colunas_para_normalizar)} colunas\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step5_header",
   "metadata": {},
   "source": [
    "## 5. Codificação de Variáveis Categóricas\n",
    "\n",
    "Aplicamos One-Hot Encoding para variáveis nominais e Label Encoding para ordinais."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "categorical_encoding",
   "metadata": {},
   "outputs": [],
   "source": [
    "df_encoded = df_normalized.copy()\n",
    "acoes_codificacao = []\n",
    "\n",
    "# Variáveis nominais para One-Hot Encoding\n",
    "variaveis_nominais = [\n",
    "    'Dim_Lojas.Tipo_PDV', 'Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI',\n",
    "    'Dim_Cliente.Uf_Cliente', 'Dim_Produtos.Grupo_Produto', 'Dim_Produtos.Sub_Grupo'\n",
    "]\n",
    "\n",
    "# Aplicar One-Hot Encoding\n",
    "for coluna in variaveis_nominais:\n",
    "    if coluna in df_encoded.columns:\n",
    "        # Limitar a 10 categorias mais frequentes se necessário\n",
    "        if df_encoded[coluna].nunique() > 10:\n",
    "            top_categories = df_encoded[coluna].value_counts().head(10).index.tolist()\n",
    "            df_encoded[coluna] = df_encoded[coluna].apply(\n",
    "                lambda x: x if x in top_categories else 'Outros'\n",
    "            )\n",
    "        \n",
    "        # Aplicar One-Hot Encoding\n",
    "        dummies = pd.get_dummies(df_encoded[coluna], prefix=coluna, drop_first=True)\n",
    "        df_encoded = pd.concat([df_encoded, dummies], axis=1)\n",
    "        acoes_codificacao.append(f\"{coluna} → One-Hot Encoding ({len(dummies.columns)} colunas)\")\n",
    "\n",
    "# Variáveis ordinais para Label Encoding\n",
    "variaveis_ordinais = ['Dim_Cliente.Sexo', 'Dim_Cliente.Estado_Civil']\n",
    "\n",
    "# Aplicar Label Encoding\n",
    "for coluna in variaveis_ordinais:\n",
    "    if coluna in df_encoded.columns:\n",
    "        le = LabelEncoder()\n",
    "        df_encoded[f'{coluna}_encoded'] = le.fit_transform(df_encoded[coluna].astype(str))\n",
    "        acoes_codificacao.append(f\"{coluna} → Label Encoding\")\n",
    "\n",
    "print(f\"Codificação aplicada em {len(variaveis_nominais + variaveis_ordinais)} variáveis categóricas\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step6_header",
   "metadata": {},
   "source": [
    "## 6. Documentação Final e Resumo\n",
    "\n",
    "Resumo de todas as transformações aplicadas no dataset."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "final_summary",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Resumo final das transformações\n",
    "print(\"=== RESUMO DAS TRANSFORMAÇÕES APLICADAS ===\")\n",
    "print(\"\\n1. TRATAMENTO DE VALORES AUSENTES:\")\n",
    "for acao in acoes_missing:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n2. TRATAMENTO DE OUTLIERS:\")\n",
    "for acao in acoes_outliers:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n3. NORMALIZAÇÃO/PADRONIZAÇÃO:\")\n",
    "for acao in acoes_normalizacao:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n4. CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS:\")\n",
    "for acao in acoes_codificacao:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "# Estatísticas finais\n",
    "print(\"\\n=== ESTATÍSTICAS FINAIS ===\")\n",
    "print(f\"Dataset original: 40.291 registros, 61 colunas\")\n",
    "print(f\"Dataset final: {len(df_encoded):,} registros, {df_encoded.shape[1]} colunas\")\n",
    "print(f\"Valores ausentes restantes: {df_encoded.isnull().sum().sum()}\")\n",
    "\n",
    "# Salvar dataset processado\n",
    "df_encoded.to_csv('../assets/dados_processados.csv', index=False)\n",
    "print(\"\\n✅ Dataset processado salvo em: ../assets/dados_processados.csv\")\n",
    "print(\"🎯 PRÉ-PROCESSAMENTO CONCLUÍDO COM SUCESSO!\")"
   ]
  },
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.13.2"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
   "execution_count": 6,
   "id": "missing_values_analysis",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== ANÁLISE DE VALORES AUSENTES ===\n",
      "Total de colunas com valores ausentes: 10\n",
      "\n",
      "Colunas com valores ausentes:\n",
      "                    Coluna  Valores_Ausentes  Percentual\n",
      "  Dim_Produtos.Segmentacao              6187   33.685414\n",
      "        Dim_Produtos.Shape              6181   33.652747\n",
      "Dim_Cliente.Bairro_Cliente               344    1.872924\n",
      "         Dim_Produtos.Cor2               216    1.176022\n",
      "    Dim_Produtos.Material2               216    1.176022\n",
      "         Dim_Produtos.Sexo               216    1.176022\n",
      "       Dim_Produtos.Griffe               216    1.176022\n",
      "Dim_Cliente.Cidade_cliente               101    0.549899\n",
      "      Dim_Lojas.Bairro_Emp                14    0.076224\n",
      "         Dim_Produtos.Cor1                10    0.054445\n"
     ]
    },
    {
     "data": {
      "image/png": "iVBORw0KGgoAAAANSUhEUgAABKkAAAJNCAYAAAD6RiYQAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjUsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvWftoOwAAAAlwSFlzAAAPYQAAD2EBqD+naQAAx9dJREFUeJzs3Qm8jGX/x/HLvoYoW5ZUWixtKin1oCKtctoXilIeKVSkRVq1S09oE3miPSmKUKhQEZFKKUUL2lDKEvf/9b2e1zX/e+bMHOc4M3PP3PN5v14nnZk5M/d6Xb/5XVsJz/M8AwAAAAAAAASoZJAfDgAAAAAAAAhJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSogi3z77bemRIkSZsyYMUFvCopI50znTucQAACEW3FjtmyIG7R9gwcPDnozAIQMSSqkrRIrzM/MmTNTvi0jR440Z511lmnQoIH9zIsvvjjha9etW2d69Ohhdt99d1OpUiXTtm1b8/HHHxfp8yZMmGA6duxodtttN1O2bFlTt25dc/bZZ5u33347CXuDZNu6das9V61bt074Gs/zTP369c2hhx5qcsW2bdvstat75s033zTZ6I033iCYBoBCJEbcT/ny5c2+++5rrrzySrNmzRqT7T777DNbD2Ry4icVrrrqKns+ly9fnvA1N954o33N4sWLTa7o37+/3edzzjnHZKMff/zRXs+LFi0KelOApCJJhbT473//G/VzwgknxH38gAMOSPm23HPPPTZB1LRpU1O6dOmEr9u+fbs5+eSTzfjx421wdu+995q1a9eaNm3amK+++mqHn6NExiWXXGI6d+5sA7t+/fqZRx991PTq1ct888035rjjjjNz5sxJ8t6huMqUKWOTmDo33333XdzXzJ4923z//ffmwgsvNLlC98xPP/1k9txzTzNu3DiTrUmqW2+9NejNAICMd9ttt9m47JFHHjFHHXWUbeBr1aqV+euvv0y2J6lUD+RakuqCCy6w/yqmTeTZZ581zZs3NwceeKDJBYrTtc+Ka15//XXzxx9/mGxMUul6JkmFsEn8DR1Iotgv8/PmzTPTpk0L5Ev+rFmzIr2oKleunPB1L730kk1UvPjii+bMM8+0j6kHlFoUb7nllgIrennggQdsi2SfPn3Mgw8+aD/P31ql4K+gJBmCDeaUUFTwcv311+d7Xue+ZMmS5txzzzVB0ReFihUrpu3znnnmGdtzrGvXruaGG24wGzdutL0LAQDhox7ghx12mP3/Sy+91NSoUcPGMhMnTjTnnXdeVtVfMKZly5Zmn332sXHNoEGD8j0/d+5cs2LFCnP33XeboKQ7rtDoDTU4qhGuQ4cO5pVXXrExDoDg0ZMKGUOV0zXXXGOHUZUrV87st99+5v7777ctHX5K9qhnk3pz6DXqit6iRQvbu6UwGjZsGJUwKihJVatWLdsTytGwPyWqFKRt3rw54d/+/fffZsiQIWb//fe3+xDv8y666CJzxBFHRH5X7yr14KlevboN3o488kgzefLkHW6nenbpJ5aGMap1KHZuBG3P448/bvbee297nA8//HDz0UcfRf2tunrr7/faay97fGvXrm26detmfv3116jXqdVJSTh9jt6rZs2atpdcYYZE/vDDD6Z79+52CJn+tlGjRqZnz55my5YtRTomCjK0Xy+88IJtTdpjjz3MLrvsYhOL69evt+dJ26htU1JSvdsKOndy9NFH232Kl4jUcEBdGxr6qW0v7LFKZMSIEbZXn46B3k897TTM1E/nt1mzZmbBggXm2GOPtcdCiSLRvihpquBT76H7R93XY/dRSWENYaxWrZo9Drp33HvsiK5nDVtVUk7Xv37XPbCz16I899xz9r7VuapSpYptvR02bFjUa3QcdO5cmaB9VE9I9XIs6nWtbRg+fLj9f/9QFkfv+dBDD9lzofOoe//yyy83v//+e9Q2zZ8/3wazGhJaoUIFe93qfANAmLVr187+q0SGv/FC5bjKQtXTqiNWrVpV6Ppr06ZNdqiSGv9U7tapU8fGXF9//XWRy2bVMaeccop57733bGyl16peHjt2bOQ1ajhUTCGqw2Onmkg0v5Le2z81xG+//WauvfZaW2+pPlUdpqTeJ598stPHd+nSpfYY61jWq1fP3HHHHVF1nZ+G3B9zzDE2oaM6VL3+9feFaYD74osv4sZoine0/0pAKg5TIkvntmrVqvZz9HnvvPNOofZl4cKF9njouOj4aOSAGqfjDStVw/G///1vG6Npv4uyj6tXr7Yxnf5O9b6un9NPP73QveT0PaJJkyb2Wjj++OPj9hJPNC+Yiz3905RolEVeXp6NA3X9abt0TygW9SvKfaOef9o+3TeKbzWqw78NinVEx8Fdz/450D744ANz4okn2vOo9/jXv/5l3n///aTF8kCq0I0DGUGJqNNOO81WgEpcHHzwwWbq1Knmuuuus8mMoUOHRr1eldrzzz9vx9irQNUXfRXCH374oS3Uk0GVrHqOqMeMn4IffRn+8ssvbYASj4IkBTEq9EuVKrXDz9JwQHWnV+ui9kktlk8//bQ9JkqInHHGGSZZFIioQlKQp8pMFZ6CQiWENNTNJTT0uyo9VbYKDLTP+leBhvtyf8UVV9jtU9JQFb0SM9r3zz//vMD5mtQ9WcfRzfmlZJ7Os95Lx0BzdxX1mCgpqApfPZ8058J//vMfuz86fwpmFXhq21V5K7EQryXR0f6df/755q677rL7rODYmTJlij23rut8YY9VPNomJdYUHClBt2zZMjukQskVBRHufIiOrYI+BTPqgahAXQGsjoeOuY6jhssuWbLE3i+6Pl999VX7t9oWBe/qwq8hHLpndIxiA5VEXnvtNfPnn3/az9Y+KnhSMKdjtDN0zBQIK3BV0kl0zWh7rr76avu7zruCKV0XulbV+1E9GwcOHGiHHepLS1Guaz2u606frV6MsfS8rg2dR11v+iKmYS4qB9y50HDf9u3b22S1rjMl/BS4qvUVAMLMJY5UF8udd95pbr75ZttwoZ5WP//8s613lYhSuanysaD6S/Mcql6aMWOGfVxlv8pwldGffvqpbXAobNnsqF5TA5XiSPWIeeqpp2xySQkB1ePaNr3Hww8/bBNlboqJok41oXpF9asSXoonFK889thjts5SUkENTkWhZIsSEf/884+tW5SYURyhmCaW6i/tmxpLVH+qrlTcoEYoHZPYBiE/xS2KOVRf+mM0nQs19CkppLr2l19+MU8++aStpy+77DJ7XkaNGmU/U3G2YvREFG/ofZSgUoOZzo+OjeIGxe7q0eWnBJXqVMVkaqwuyj4qIaTP6927t31MdbSun5UrVxZ4HEQNeS+//LJtHBftq64xnQvFOUWlxJ62V++r7dF7KH6ZNGmSjXWVJCrqfaPYVd9tFMvo9Yp9BwwYYL976H7SdauYTsdOMaCOuyh2FvUQ0+t0/asxU/Hw6NGjbTL03XffjTSU72wsD6SUBwSgV69e6h4V+f3VV1+1v99xxx1RrzvzzDO9EiVKeMuXL488ptfpZ/78+ZHHvvvuO698+fLeGWecUaTtqFSpkte1a9eEz3Xr1i3f45MnT7afP2XKlITvO2zYMPuaCRMmFGo7+vTpY1//7rvvRh77448/vEaNGnl77rmnt23bNvvYihUr7OtGjx4ded2//vUv+xNL+9WwYcPI7+5va9So4f3222+RxydOnGgff/311yOP/fXXX/ne79lnn7Wvmz17duSxqlWr2nNZVF26dPFKlizpffTRR/me2759e5GOyTvvvGNf16xZM2/Lli2R15533nn22unYsWPU+7dq1SrquCSydOlS+74DBw6Mevzcc8+119r69euLdKx0zvSYzoOsXbvWK1u2rNe+ffvIvsgjjzxiX/fUU09FHtP51WOPPvpo1Of897//tcfRf4xEr9Pr33//ffv70KFD7e8///yztzNOOeUU7+ijj478/vjjj3ulS5e2++BX2Gvx6quv9qpUqeL9888/CT/z9ttvt/fgl19+GfX49ddf75UqVcpbuXJlka/r2HLH0fHT4+PGjYt6XPe4/3Hdz/o93nULAGHg6qrp06fbOmPVqlXec889Z8vYChUqeN9//7337bff2nL4zjvvjPrbJUuW2LrB/3ii+kt1nB5/8MEHE8YBhS2bRXVMbL2rOqpcuXLeNddcE3nsxRdftK9T7BBLj99yyy35Htd7+2PFTZs2RdXbri7SZ912221Rj8XGbPG4eOeDDz6I2nbFWP64QTFQtWrVvMsuuyzq71evXm1fG/t4PIcffrhXr169qO13x/Oxxx6zv6tu3rx5c9Tf/f77716tWrXyxcWxx6xTp042tvn6668jj/3444/eLrvs4h177LH5rrPWrVtHxQKF3Udtj/7+vvvu83bGSy+9ZP/+q6++sr9v2LDBxnaKl/xiYzfHxZ7uOlq4cKH9XddXIjtz34wdOzbymM5J7dq1vby8vMhjikfiXWO6hxo3bux16NAhcj+5mFVx9AknnFDsWB5IJYb7IWMmNFaPI7Vw+amFQ3Vg7GpimrxTLQOOWn7UxVe9r9QilAwa0qQeJ7HUhdc9n8iGDRvsv+qiXNj9V4uGf0U5dZFWy4h6aqhlLlm0gsmuu+4a+d21vKhl0PG33qk7vlrVNNRO/N1/1eKjrsTqoVJY6v2jFshTTz01Mt+Fn+t5VNRj0qVLl6gWVbXW6dqJHYqlx9WtWi2WBVFr0iGHHGKHpTlq5VOvIrX+qpWwKMcq1vTp023Lm3rb+XvrqdVS7x07rFHXolr5/DRfmlrS1BNNn+t+3LAM1zXftcxpiF6i4QOJqEVN95V/DhK1XrohljtD26NjqRbPRLRvujZ1rfr3Tb3OdI/HDu8tzHVd0GeplVPd2/2fpTJG11zscVTLqIZ9AkBYqaxVDxcNt1ZPJ5WFGvatIUfqPaq6RL07/GWmeo80btw437CwePWXerFo2LR6nSSKAwpbNvvrbVf2i7ZfQ9sLUw8UhfbH1duqj1RPumH0OzNESvGO4gb/FBDadtdj21GdqV45qo/9x0Pxs2KbwgzHU082zcPkr0PVs0o92N1QSL2ffhedZ/UeV8ykmK2g/dOxeOutt0ynTp3sUEtHw/DU81q9c1x87I95/CMOCruPir20jRryFjv0szDUG1z7o2kExA0p3NmFYVxPKcVLiRYXKOp9o2vKP3ev9lfXSGGuZ02kruGHOu66Pt1nKfZSL3adfxcP7kwsD6QaSSpkBK2ipu7RsUkd1wU7dpU1FeaxNKeBKgZ1nU0GVYDx5i5SIsI9n4hLYBR2pRDtn4KbWIn2vziU0PNzX+z9lbwCEnW9V5d87aeCJXVpF//Yeg2pUrd8BbGqODV8bUeVp86PgpQdDcss6jGJ3S8XMGjbYh9XxRw7R0A8ChA1tMCtwqjkmq4xf+BY2GMVb/8kdh8VhCi4i90/fTFwQaOjAERd3fWZ/h/dC6Ku7y6Bo3m21LVc26kvHEowFSZhpWG1SsgoYaehFPrRPitY3NlgTt37tY3qhq45G5RI1DDK2H3TY7H7pi9O/n0rynWdiD5L50rzMMR+noY5us/SUA4l6DRcQl+ulBhX1/kdzXEGANlGc/gpYaAvzmoUUt2u4UyuzFQjkGKx2DJTQ4Riy+d49ZeGD6r+K2gBmcKWzYnqAVcX7EwSoyCqOzWsXvuvhJXqA22T5qgsTGwRS/V9vLg2Nj5wK0urISr2eCg5FHs84lH9r4SPm3NTMa2Sj6qP/Q09ml5BUwSoYVZDPPUZajwraP8U3ylGShS76bjFzr3k4qWi7qOOu4YCqhFbcY2Gyykm1XC9HVESTIlB1ekurtGP4iTNO6npEopK+6FVvDVMUteD7hXdQ/7jVdT7RvFR7JQRhb2e3XHUsMnYz9I2Km5x27YzsTyQasxJBSSglh/NfRPLPVbQnAPq2SKaH0gtSqmkCix2cnlJ1KMs0RxZ/vdQK48SM5oTTHMPqDVHwYXGxvsTG3qdWi0V4Ch4uO+++2zQoNYiBTzplGi/CrO/iaglT3MqKJjTGH/9qwDhpJNOKvKxKq54SVG9v+Ym0IpL8bgEnf5WrWb6sqEgU8kfJZ8UBOq8FTRvmktEKXiLR4GMazEt7LWoLxxq5VOLowJM/SjZo95wCozdvqn1XMc/HpeIS8Z51mdpmxIl3RTUuf3TvA2aa0zLVWv7lWDTSp56rKDVQgEgm+jLarzezq7MVHmosjte2RtbFhbUqJeMsjkZ9UBBYuswzVepeYVU/t9+++128mv1rFLP6GTW+7Hce2vOpnjzJhVmxWg3KbZ6simJorpMDar+xjdN7K25vBS/KrbR3+jYau5P/6T2yRB7bRRlH3W81StfDYiqj3VOtI2ai0kNa4moh56SNKq79RNL15saoyTRvKLxYmy9l46beq0rttLoEG2P4gMlnIp63xQ3rhHF5YnmEHOfl0mxPOCQpEJG0Ip7Gv6kitLfm0qrkLjn47UQ+KnlQytXxAYtO0uFuiYWVEHvH46lLrH6nNgvyX4aoqZkhpb61eScO5o8XfunSbNjJdp/P31OvBaPne19pRYaTWSqCto/uXi8Y+6SeeoZox+1AmmSRU0Mmahi0/lRTzO12qTqmCSLEpGazFQBjYIftSorAHEtwkU9Vn5u+7WP/m7xGgKo3luux1BBNLGsVhNS1+0drVipa1iv04+SWgqyb7zxRpu4SvRZrheZJtNUi6Of7gutUKnE3U033VTka1HHUMGlfvReun40uaqOs7rfa9/UUl6Y41BYiY6RPkvljxJxhfkypWEZ+tF1rv1XcK9hoeqpBgBhpzJTX5TVe6SgWGhH76F4Sj11/UP1i1M2F0ZBdaXqsNjVdVUnxzZYuhV+NZm4n/5WvWiKSvFAvLghNgZyk8kraVSculF1lhqrlCxRHaaYTHWxf/8UlyhJ4T9emny7IIrvFB8nit0Uh8T2bo9V1H3U6zU1iH50DBW7K1mkRFsiSkKpN3+8/VEcomPiklSud1nsdZEoxlbDoX4UFyl+0rX76KOP2tUak3HfFCWuEZ3bwhzHosbyQKox3A8ZQT1T1CqhFVv81J1aBXBsITl37tyocfHqPqyWC628VZjV9ApDK8RoxRb/yl0az62EhSrzePNVOaqktQKHuu/q33itHqpAtUqK23/9v/bL0bhxre6iFUo0z0IiqohU+fuHOSpxUdiV22K54xe7zbGrqel8xXb7VlChxE5Bw58UpKh1Tq136lYdy31ucY5JMimYU4WtFYYUTPtbGwt7rOJR0KBEjVYZ8v+9gl4dV82NsCNq/dLqMU888US+5zRnmlspR8PzYrmWtYLOlWu9Vm8m3Q/+H322Elf+Fu7CXouaHyH2mtCwAv/26P117tU6GkvB4o7mFItHKya5v/fTZ+l6Vot4LH2Oe72SkrHnujDHEQDCRKuNqf7TF/nYMlG/x5bx8WjotGKq2LjPvUdRyuZk1AOuDoud71AxR2yvGe177H4rNlR9vDMU76i3jYsJRfVobA8yDSFT0kGNTPHmRSzsdBeKwRSnamVsJap0Pt18q4liGyUU/fFYPPo7xeGKxzV3qKNYWokfNeC66TASKew+alihm37Df/7U0F1QfazvCzrHurZi4xr9aO40Df3T/rr3FP91oetB14WfprGIjUuUrFJ847YnGfdNYa9nzdumbb///vttg1+i47izsTyQavSkQkZQ0ketUurZoYrtoIMOsl1OVdGpO6+rJBy1gKgiU1daJYtU0Ypr+SiIkiP64iyqADWHgFo45LTTTot8WVZlpd4SqrA0H4Nax/Q5KtAL8znqIq35gtSio94qej91XdZ4eXVNVjDi5jrSksPqdaVknPZJXcc17Ek9WdQl29+TK5a6m6tnjI6Hll1WQkWtNlpuOXaCysJQcODG9uv4aC4JnQtti596van7svZL50vdhtXi+dFHH8XtPu2n4EPvqSSHJkLXXAVqqVSQp4k1NYljcY5JMimQVsuSrkW1AOrYFPVYJWpxHDhwoL2WNDRQ155aH3WNHX744VGTZSainkyaW0rLB+saU4udrk8livS4EjwarqElihVgKfGlFltdI/ocnT//xPSxFCArCZOo5VPbrElvlTBWq1thr0X1OFLiTMMNtQ1qkdQSzPosN+eY7h83Sb1bQlxJNw2hVSuvyomitli7xRZ0PWkbFSxqfg5dh0pCqlu+hiEqyFbLvlpldU0OGzbMXue6/nTczjjjDFsm6R5QglDXgX8IKACEmco/xU2qw1QWK+mh5IDqPg0ZUr1+7bXXFvgeGt49duxYO4+P4iENN1IZrzhCda7m/Cts2VwUqmdU9ms4k76cK4ZUXaQv5qqbVJ+q3teQOMWKqkdj6xrVS6pXFR9qKgDVS6ov/b2ii0INQRreplhAc1wq8aAkiOprxaiO6pqRI0faul91ruovxRIrV660Q/kVA8RL+sVSvKZz5ualip2gXfunBlrVdYobdF5Vl6txMF7Cw0/XhXqdK7bQedTwPPVOUsJDsdKOFHYfNXpCPcOVbNJ26XN07Skhpr9JRPushJDil3hUl+u9dD4196biF30X0LWuuEWxqHpOxyakNMRQvc41+bx6Sel5nVNda7qeknXfxNJ7KmbW+dF76drRdqu3luaeUgytfdC1qhhViVTFizrObqjnzsbyQEqldO1AIIF4S8Fr2dm+fft6devW9cqUKWOXTtXSsv6lU0V/p79/5pln7Gu05O8hhxwSdznheLSMsN4j3k/sEq5a0r579+526eWKFSvaJWGLuvy8lrlt3769V716dbvEbJ06dbxzzjnHmzlzZtTrtFzvmWeeaZfe1TK4RxxxhDdp0qSo1yRazljHYq+99rLL/h588MHe1KlT7X5q2eTYv423XG/sEsJaYvqMM86w26Klac866yy7hLD/dVoK97rrrvMOOuggu7RwpUqV7P+PGDGiUMflu+++87p06eLtvvvu9hxq+3Ve/cseF+aYuGWAY5f9dcsGx54vbb8e19LahaX919/0798/33OFOVYFLWP8yCOPePvvv7+95rW8c8+ePe3Syn667po2bRp327Zs2eLdc8899nkdx1133dVr0aKFd+utt3rr16+3r5kxY4Z3+umn23tL14j+Pe+887wvv/wy4T4vWLDAbu/NN99c4HLKeo3u26Jci+6eqFmzpn1dgwYNvMsvv9z76aef8pUJAwcO9PbZZx/7ut1228076qijvPvvv9/ud1Gvay1z3bt3b3vNlShRIl8Z9Pjjj9tjp2XWdU03b97cnnOdT/n444/tcdP26lhr+0855RRv/vz5CY8RAGSTRHVnPC+//LLXunVrW//rR3WZ6vFly5YVqv7666+/vBtvvNFr1KiRrQNr165t63zV/UUpm0V1zMknn5zvM/T5+vF74oknbD1VqlQpu68ufty2bZs3YMAAW9co5uvQoYO3fPly+96qx5xNmzZ511xzjY3ntE1HH320N3fu3HyflShmi2fx4sX2bxXr7LHHHt7tt9/ujRo1Km7coO3Vtinm0Ov33ntv7+KLLy5SXTR58mT73toH7bef4u677rrL7reLsRV7xdbl8epZV1dq+ypXrmyPY9u2bb05c+YU6Trb0T7+8ssv9lrTNadrT69r2bKl98ILLxS437p2VIcXpE2bNrZ+37p1q/1d1+Pxxx9vj4XitBtuuMGbNm1a1LXzzTffeN26dbPbqe1VzK/9nj59elLvm3jnYOLEiV6TJk3sd4zY623hwoVe586d7fcYbb/+9uyzz7ZxYTJieSBVSug/qU2DAcml4X+9evUqVGsRAAAAAADIDsxJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcKzuh6zDNGoAAAAAAIQPPakAAAAAAAAQuJzpSbV9+3bz448/ml122cWuDgcAAMLTw/aPP/4wdevWNSVL0v6WbMRQAACEj5eh8VPOJKkUXNWvXz/ozQAAACmyatUqU69evaA3I3SIoQAACK9VGRY/5UySSq1/7gRUqVIl6M0BAABJsmHDBptEcXV9rhg5cqT9+fbbb+3vTZs2NYMGDTIdO3a0v7dp08bMmjUr6m8uv/xy8+ijjxbpc4ihAAAInw0ZGj/lTJLKdU9XcEWABQBA+OTaUDS1et59992mcePGtsv+008/bU4//XSzcOFCm7CSyy67zNx2222Rv6lYsWKRP4cYCgCA8CqRYfFTziSpAAAAwuTUU0+N+v3OO++0PavmzZsXSVIpKVW7du0ive/mzZvtj7+lFQAAIB0yZ3YsAAAA7JRt27aZ5557zmzcuNG0atUq8vi4cePMbrvtZpo1a2YGDhxo/vrrrx2+15AhQ0zVqlUjP8xHBQAA0oWeVAAAAFlqyZIlNim1adMmU7lyZTNhwgTTpEkT+9z5559vGjZsaFftWbx4sRkwYIBZtmyZeeWVVwp8TyWz+vXrl2/OCgAAgFQjSQUAsEvMb9myJejNAOIqU6aMKVWqVNCbkZH2228/s2jRIrN+/Xrz0ksvma5du9rJ0pWo6tGjR+R1zZs3N3Xq1DHHHXec+frrr83ee++d8D3LlStnfwAAudETd+vWrUFvBlKgTJbGTySpACDHKTm1YsUKm6gCMlW1atXs3EqZNrln0MqWLWv22Wcf+/8tWrQwH330kRk2bJh57LHH8r22ZcuW9t/ly5cXmKQCAISfFtxYvXq1WbduXdCbghSqloXxE0kqAMjxAOWnn36yrSwazlOyJFMVIvOuUc2jtHbtWvu7egMhMSWb/ZOe+6nHlXAMAQAuQVWzZk27yEY2JTEQ7viJJBUA5LB//vnHVmCas2ZnlqYH0qFChQr2XwVaCqazset6KmjuqI4dO5oGDRqYP/74w4wfP97MnDnTTJ061Q7p0+8nnXSSqVGjhp2Tqm/fvubYY481Bx54YNCbDgAIeIifS1CpjkA4VcjS+IkkFQDkeJDihgwBmcwlUTVvRrYEWammoLNLly62N6RW4VPySQmqE044waxatcpMnz7dPPTQQ3bFP/WUzMvLMzfddFPQmw0ACJibg4oGyvCrmIXxE0kqAABdvJHxuEbzGzVqVMLnlJTSBOoAACRC3Rp+JbLwHDP5CAAAAAAAAAJHkgoAAAAAAACBY7gfACCfTf3uTevnlX+wf1o/r02bNubggw+28/UAAAAg/PFfGH377bemUaNGZuHChTa2DQN6UgEAssqpp55qTjzxxLjPvfvuu3bsvVYyC4sOHTrYiS4/+ugjk4kuvvhi06lTp6A3AwAA5ADFHYr19KOFf/bZZx9z22232RWrM5m299VXXw16M7ICSSoAQFbp3r27mTZtmvn+++/zPTd69Ghz2GGH2VXOUr0q4vbt202qrVy50syZM8dceeWV5qmnnkr55wEAAGQ6NVZqZduvvvrKXHPNNWbw4MHmvvvuy9h4DkVDkgoAkFVOOeUUs/vuu5sxY8ZEPf7nn3+aF1980fbqOe+888wee+xhl91t3ry5efbZZwt8z99//9106dLF7LrrrvZvOnbsaAMfR59VrVo189prr5kmTZqYcuXK2QTS5s2bzbXXXms/q1KlSqZly5Zm5syZkb/77rvvbM8vva+eb9q0qXnjjTcKva9Kuml/e/bsaffh77//jnp+zz33zDdkUV29FayJ53n2/xs0aGC3uW7duuaqq66KvHZH2+/2e+rUqeaAAw4wlStXjgSGovd++umnzcSJEyOtmu7vV61aZc4++2z799WrVzenn3667ZLu6HVHHHGE/Vy95uijj7bHCwAAoCCKaWrXrm0aNmxoY6Tjjz/exmiFjWvixXMDBgywK+PqMfXO8q+g++mnn9rYUHFQrVq1zEUXXWR++eWXqGkkFF/179/fxjzaNheLuXhNzjjjDBsrud/j9Ubv06ePfT9nypQppnXr1na7a9SoYePCr7/+2oQZc1JlwLjcdIzFDct+AEDp0qVtQkmBxo033hhZWlcJKrWIXXjhhfb/FWxUqVLFTJ482QYTe++9t02KxKMgQUkpBS36G/3tSSedZD777DNTpkwZ+5q//vrL3HPPPebJJ5+0QULNmjVtDye95rnnnrMJoAkTJtgkzpIlS0zjxo1Nr169zJYtW8zs2bNtsKTXKsApDCWYlKQaPny42X///W3A9NJLL9l9KayXX37ZDB061G6fEmSrV682n3zySeT5HW2/2+/777/f/Pe//zUlS5a0x1cB4Lhx4+y/n3/+udmwYYPdVlFwtnXrVjtMsVWrVnYIps7ZHXfcYd9bQzH1PgrKLrvsMpt80zH68MMPs3KZZIQ/7gjTvgBAGFWoUMH8+uuvhY5rYuM5xZVz5841Dz/8sDnooIPMihUrIkmodevWmXbt2plLL73UxlRqMFScqIa4t99+O7INarTr16+f+eCDD+x7KbZUA9wJJ5xgp2zQ5yhW0vZoGofC2rhxo31fjRJQg+ygQYNssmvRokU2ngojklQAgKzTrVs326171qxZkdYmVfx5eXm2VU3JE6d37962J9ALL7wQN0nlklPvv/++Oeqoo+xjSsCoNU1zB5x11ln2MSVeRowYYYMXUcubPlP/KhASfa5avPT4XXfdZZ/TNqk3l+y1116F3sfp06fbQErJHlFySK16RUlS6fPVmqcWRiXb1KPKHYPCbL/b70cffdQm+UQBoOZ+ECXcFBiqBVKf4zzzzDO2+7wCQJd40nuqFVAtmhqSuX79etsa6N5XPbUAAAAKSw16M2bMsHGeetEXNq7xx3NffvmljRE1lYTipdh47ZFHHjGHHHJI5O9FUzAoTtTf7rvvvvYxJZFuueUW+/9KiOnvtG1KUmkEgCgO8sdLhZGXlxf1uz5b76dkXLNmzUwYhTP1BgAINfUsUkLJzdO0fPly22NH81WpN9Xtt99uE0Pq1aNEioIXBS3xqCeQevqoS7ijlrX99tvPPudock7/XFdqldNnKTjRZ7gfJc5cN2x1/VYPIrWkKXApyoTu2rdzzjnHbpso+FIirShdvJVgU4ufgi31WlKLoptYtDDbLxr+6BJJUqdOHbN27doCP1e9tXROdtlll8j76lxs2rTJvrf+Xy2MSsBpOOSwYcMiQwgBAAAKMmnSJBtblC9f3g7DU7x05plnFiquiY3n1CNJPZv+9a9/JYxp3nnnnaj3VBwq/veNnQ+1MPFSYXz11Vc2BlQsp97+bqhgorg2DOhJBQDISkpIqZeUhsOphUyJFAUY6sKtpIfmalKiSsPsNL5fQ8qKQz2G/MPR1OVaQc2CBQvyddt2Q/rUNVyJGA05fOutt8yQIUPMAw88YLe7IL/99ptNKKm1b+TIkZHHFXwpeXXnnXfa39XNW62IfvobR618y5Yts72y1EL473//O9IDrTDbL264o6NjEPuZsfTeLVq0sD3SYrnWRJ0zJfHUwvn888+bm266yW7jkUceWeB7AwCA3Na2bVsbHynhpF5TatBTLFGYuCY2ntPvO4pp1KCm+DKWElEFxUs7mpR9R3Gc6LM1SuCJJ56w+6r3VA+q4sa1mYwkFQAgK2kugKuvvtqMHz/ejB071k6cqYBAvY00SbeGx4kqc3XH1gSZ8WiYmXoXaQ4BN9xP8xoouZPob0Rdv5U0UivZMccck/B1ShRdccUV9mfgwIE2yNhRkkrJnXr16uVbqliJLiW5NNxOAZgSPv4eSJobSvMo+Cn4UoCjH82RpdY/9aIq7PbviAJEvY/foYceaoNFzb+gVr9EtA360XHR/FU6lySpAABAQdQAqbk6/XY2rlGDpmJFNeC54X6xMY3m+FQPJte7fWcoiRUbLymO06TsfosWLYokvFw8qtjR7dN7771nwo7hfgCArKRWMXXvVoJDiRoNH3PzAKhHzpw5c+xwvcsvv9ysWbMm4fvo9UpqaTicKn5161aCSyvD6PFE1J38ggsusJNtvvLKKzY5pMm/1VtKPadEPbg01FDPffzxx7a7eGHmXtLcU+q2rpYy/496j2kiT/U+Ek3kqQnNNdRRiaeuXbtGtR5qcnm9lwKgb775xs4VpaSVWuQKs/2FoaBNwxgVRGnb1AKo991tt93s8dO26b01F5V6Tn3//ff2d503TSyqFf2UfFN3dualAgAAO2Nn4xrFMYqfNN+pGgddzKJ5qkQNfOrhriF3mgBdQ/wU211yySX5kk4F0edojiotYqNVpV0cN3/+fNvYqjhIU0N86ktaaXVoTUHx+OOP22kUNFG7JlEPO3pSAQCydqUpJW2UhNFKfG6STA0bU0JGw+w0n1KPHj3sSnKaqDsRDT1TryxN5K3u08cee6x544038nXdjvd3mnPqmmuuMT/88INNzKgnkN5HFLwouFFiRj2KtKKLVoYpiLqpK1GmVrNYVatWNccdd5zd55NPPtkmehRM6fP0nObi8vek0gSdd999tw1otC1qLXz99ddtwFOY7S8MJffcZOjqEq9EnCaz14qGWv2mc+fO5o8//rBJP227joPmyfriiy/sSjhqJVR3eR0nJRQBAEAwsiX+S2Rn4xoNHbzhhhvstAiKS7TQjH4XxZfqpa+Ypn379naxGDX2KaYryup66gmveEzxnWKib7/91saqN998s+nfv7+dt1OJsi5dutiGR9H7a6VCNfKpsVLzpWoFQrdoUFiV8HY0scQO6OTrhL355pt2FSJ1u9PFoWBV9PbKCOpkaPlGTR6ri8AtASnKTGrogwJnnQjNYK/5RPxjR9VKqwBW2Ut1i9PrdTILS0MgFMDrS0pBQw/CuuxwWPYDQHKpQlRSo1GjRnbySSAbr9WdreNRODtzfMMUd4RpXwBAiP9yx6YsjJ+KNdxP3dSUdFJLs5JUWgZRGUJ1S3Puvfdem+3T8tWa70PjR5Ux1MFy1C1v6dKldniGZupX66tavv0HT1lLZSzVwqxJXwcPHmy7vQEAAAAAACDHh/tphntNCKueU44ydI56UWl1JQ29cPN6aLxlrVq17HjPc889184Xork11EPK9b76z3/+Y4du3H///bZ7nSaQ1fALrWikCVqbNm1qJxR78MEHo5JZAABkA02irvmh4tF8WGrYAQAAAHJNsXpSvfbaazaxdNZZZ9kVfDSjvn8ODXUr08Rg/lny1Z2sZcuWdrJU0b+aM8MlqESv17A/9bxyr9H8IEpQOeqNpUla3aRjsTRWVD2w/D8AAGQCrc6nxpZ4P3oOAAAAyEXF6kmliWk1v5QmANPEYuoNpUm9lEzSDPlKUIl6Tvnpd/ec/lWCK2qjSpc21atXj3qNv4eW/z31nH94oaNZ/G+99dbi7B4AACmhei+27gMAAAByXbF6Um3fvt0ceuih5q677rK9qDT0Tqv8ZMIwBa14pAnA3M+qVauC3iQAyFjFXEMDSDmuUQAAkkvf5xFu27PwHBerJ5WWjG7SpEnUYwcccIB5+eWX7f/Xrl3b/rtmzRr7Wke/H3zwwZHXrF27Nuo9/vnnH7vin/t7/au/8XO/u9fEKleunP0BACRWqlQp+6/m/atQoULQmwMkpBWERYu1AACAnaeRT5pe58cffzS77767/b1EiRJBbxaS3Li3ZcsW8/PPP9tz7Z86KdRJKq3sp3mh/L788ku7Cp9oiJ6SSDNmzIgkpTQ3lOaa6tmzp/29VatWZt26dXbVvhYtWtjH3n77bZvx09xV7jU33nij2bp1ayQ41UqA++23X9yhfgCAwtHw6ooVK9oKTOWrKjEg04IsJajUoKU5LF1iFQAA7BzFe/qu/tNPP9lEFcKrYsWKpkGDBlkV4xcrSdW3b19z1FFH2eF+Z599tvnwww/N448/bn9E2dg+ffqYO+64wzRu3NjeCDfffLNdsa9Tp06RnlcnnnhiZJigElFXXnmlXflPr5Pzzz/fzi/VvXt3M2DAAPPpp5+aYcOGmaFDhybjGABAzlI5rZ6uWujiu+++C3pzgISUoErUexoAABSNetYoeaFRTNu2bQt6c5ACpUqVsg3S2dZLrlhJqsMPP9xMmDDBzv+k1YiUhHrooYfMBRdcEHlN//79zcaNG+18Veox1bp1azNlyhRTvnz5yGvGjRtnE1PHHXeczfDl5eWZhx9+OGpFwLfeesv06tXL9rbabbfdzKBBg+x7AgCKH6SoIUFdgoFMpF5+9KACACC5lLxQHctQeoQmSSWnnHKK/SnowlcCq6AltbWS3/jx4wv8nAMPPNC8++67xdpWAEB8aiDwNx4AAAAAQLplz8BEAAAAAAAAhBZJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAACALDRy5Ehz4IEHmipVqtifVq1amTfffDPy/KZNm0yvXr1MjRo1TOXKlU1eXp5Zs2ZNoNsMAABQEJJUAAAAWahevXrm7rvvNgsWLDDz58837dq1M6effrpZunSpfb5v377m9ddfNy+++KKZNWuW+fHHH03nzp2D3mwAAICESid+CgAAAJnq1FNPjfr9zjvvtL2r5s2bZxNYo0aNMuPHj7fJKxk9erQ54IAD7PNHHnlkwvfdvHmz/XE2bNiQwr0AAAD4f/SkAgAAyHLbtm0zzz33nNm4caMd9qfeVVu3bjXHH3985DX777+/adCggZk7d26B7zVkyBBTtWrVyE/9+vXTsAcAAAAkqQAAALLWkiVL7HxT5cqVM1dccYWZMGGCadKkiVm9erUpW7asqVatWtTra9WqZZ8ryMCBA8369esjP6tWrUrxXgAAAPwPw/0AAACy1H777WcWLVpkk0kvvfSS6dq1q51/qjiU8NIPAABAupGkAgAAyFLqLbXPPvvY/2/RooX56KOPzLBhw8w555xjtmzZYtatWxfVm0qr+9WuXTvALQYAAEiM4X4AAAAhsX37djvpuRJWZcqUMTNmzIg8t2zZMrNy5Uo7ZxUAAEAmoicVAABAFtLcUR07drSTof/xxx92Jb+ZM2eaqVOn2gnPu3fvbvr162eqV69uqlSpYnr37m0TVAWt7AcAABAkklQAAABZaO3ataZLly7mp59+skmpAw880CaoTjjhBPv80KFDTcmSJU1eXp7tXdWhQwczYsSIoDcbAAAgIZJUAAAAWWjUqFEFPl++fHkzfPhw+wMAAJANmJMKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACA7E5SDR482JQoUSLqZ//99488v2nTJtOrVy9To0YNU7lyZZOXl2fWrFkT9R4rV640J598sqlYsaKpWbOmue6668w///wT9ZqZM2eaQw891JQrV87ss88+ZsyYMcXZbAAAAAAAAIStJ1XTpk3NTz/9FPl57733Is/17dvXvP766+bFF180s2bNMj/++KPp3Llz5Plt27bZBNWWLVvMnDlzzNNPP20TUIMGDYq8ZsWKFfY1bdu2NYsWLTJ9+vQxl156qZk6dWpxNx0AAAAAAAAZonSx36B0aVO7du18j69fv96MGjXKjB8/3rRr184+Nnr0aHPAAQeYefPmmSOPPNK89dZb5rPPPjPTp083tWrVMgcffLC5/fbbzYABA2wvrbJly5pHH33UNGrUyDzwwAP2PfT3SoQNHTrUdOjQIeF2bd682f44GzZsKO6uAgAAAAAAIFN7Un311Vembt26Zq+99jIXXHCBHb4nCxYsMFu3bjXHH3985LUaCtigQQMzd+5c+7v+bd68uU1QOUo8KaG0dOnSyGv87+Fe494jkSFDhpiqVatGfurXr1/cXQUAAAAAAEAmJqlatmxph+dNmTLFjBw50g7NO+aYY8wff/xhVq9ebXtCVatWLepvlJDSc6J//Qkq97x7rqDXKJH1999/J9y2gQMH2t5c7mfVqlXF2VUAAAAAAABk6nC/jh07Rv7/wAMPtEmrhg0bmhdeeMFUqFDBBEmTrOsHAAAAAAAAOTDcz0+9pvbdd1+zfPlyO0+VJkRft25d1Gu0up+bw0r/xq72537f0WuqVKkSeCIMAAAAAAAAGZik+vPPP83XX39t6tSpY1q0aGHKlCljZsyYEXl+2bJlds6qVq1a2d/175IlS8zatWsjr5k2bZpNQDVp0iTyGv97uNe49wAAAAAAAECOJ6muvfZaM2vWLPPtt9+aOXPmmDPOOMOUKlXKnHfeeXay8u7du5t+/fqZd955x06kfskll9jkklb2k/bt29tk1EUXXWQ++eQTM3XqVHPTTTeZXr16RYbqXXHFFeabb74x/fv3N1988YUZMWKEHU7Yt2/f5BwBAAAAAAAAZPecVN9//71NSP36669m9913N61btzbz5s2z/y9Dhw41JUuWNHl5eWbz5s12VT4lmRwltCZNmmR69uxpk1eVKlUyXbt2NbfddlvkNY0aNTKTJ0+2Salhw4aZevXqmSeffNK+FwAAAAAAAMKhWEmq5557rsDny5cvb4YPH25/EtFE62+88UaB79OmTRuzcOHCnd5OAAAAAAAA5NCcVAAAAAAAAMDOIEkFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAstCQIUPM4YcfbnbZZRdTs2ZN06lTJ7Ns2bKo17Rp08aUKFEi6ueKK64IbJsBAAAKQpIKAAAgC82aNcv06tXLzJs3z0ybNs1s3brVtG/f3mzcuDHqdZdddpn56aefIj/33ntvYNsMAABQkNIFPgsAAICMNGXKlKjfx4wZY3tULViwwBx77LGRxytWrGhq164dwBYCAAAUDT2pAAAAQmD9+vX23+rVq0c9Pm7cOLPbbruZZs2amYEDB5q//vqrwPfZvHmz2bBhQ9QPAABAOtCTCgAAIMtt377d9OnTxxx99NE2GeWcf/75pmHDhqZu3bpm8eLFZsCAAXbeqldeeaXAua5uvfXWNG05AADA/yNJBQAAkOU0N9Wnn35q3nvvvajHe/ToEfn/5s2bmzp16pjjjjvOfP3112bvvfeO+17qbdWvX7/I7+pJVb9+/RRuPQAAwP+QpAIAAMhiV155pZk0aZKZPXu2qVevXoGvbdmypf13+fLlCZNU5cqVsz8AAADpRpIKAAAgC3meZ3r37m0mTJhgZs6caRo1arTDv1m0aJH9Vz2qAAAAMg1JKgAAgCwd4jd+/HgzceJEs8suu5jVq1fbx6tWrWoqVKhgh/Tp+ZNOOsnUqFHDzknVt29fu/LfgQceGPTmAwAA5EOSCgAAIAuNHDnS/tumTZuox0ePHm0uvvhiU7ZsWTN9+nTz0EMPmY0bN9p5pfLy8sxNN90U0BYDAAAUjCQVAABAlg73K4iSUrNmzUrb9gAAABRXyWK/AwAAAAAAAFBMJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAcCWp7r77blOiRAnTp0+fyGObNm0yvXr1MjVq1DCVK1c2eXl5Zs2aNVF/t3LlSnPyySebihUrmpo1a5rrrrvO/PPPP1GvmTlzpjn00ENNuXLlzD777GPGjBmTzE0HAAAAAABAGJJUH330kXnsscfMgQceGPV43759zeuvv25efPFFM2vWLPPjjz+azp07R57ftm2bTVBt2bLFzJkzxzz99NM2ATVo0KDIa1asWGFf07ZtW7No0SKbBLv00kvN1KlTk7X5AAAAAAAAyPYk1Z9//mkuuOAC88QTT5hdd9018vj69evNqFGjzIMPPmjatWtnWrRoYUaPHm2TUfPmzbOveeutt8xnn31mnnnmGXPwwQebjh07mttvv90MHz7cJq7k0UcfNY0aNTIPPPCAOeCAA8yVV15pzjzzTDN06NCE27R582azYcOGqB8AAAAAAACEOEml4Xzq6XT88cdHPb5gwQKzdevWqMf3339/06BBAzN37lz7u/5t3ry5qVWrVuQ1HTp0sEmlpUuXRl4T+956jXuPeIYMGWKqVq0a+alfv34ydhUAAAAAAACZmKR67rnnzMcff2yTQrFWr15typYta6pVqxb1uBJSes69xp+gcs+75wp6jRJZf//9d9ztGjhwoO3J5X5WrVpVzD0FAAAAAABAqpQuzh8r8XP11VebadOmmfLly5tMognW9QMAAAAAAICQ96TScL61a9faVfdKly5tfzQ5+sMPP2z/X72dNK/UunXrov5Oq/vVrl3b/r/+jV3tz/2+o9dUqVLFVKhQoTi7AAAAAAAAgGxPUh133HFmyZIldsU993PYYYfZSdTd/5cpU8bMmDEj8jfLli0zK1euNK1atbK/61+9h5JdjnpmKQHVpEmTyGv87+Fe494DAAAAAAAAOTzcb5dddjHNmjWLeqxSpUqmRo0akce7d+9u+vXrZ6pXr24TT71797bJpSOPPNI+3759e5uMuuiii8y9995r55+66aab7GTsbrjeFVdcYR555BHTv39/061bN/P222+bF154wUyePLk4mw8AAAAAAIAwJKkKY+jQoaZkyZImLy/PbN682a7KN2LEiMjzpUqVMpMmTTI9e/a0ySslubp27Wpuu+22yGsaNWpkE1J9+/Y1w4YNM/Xq1TNPPvmkfS8AAAAAAABkv6QnqWbOnBn1uyZUHz58uP1JpGHDhuaNN94o8H3btGljFi5cmLTtBAAAAAAAQEjmpAIAAAAAAACSgSQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAABZaMiQIebwww83u+yyi6lZs6bp1KmTWbZsWdRrNm3aZHr16mVq1KhhKleubPLy8syaNWsC22YAAICCkKQCAADIQrNmzbIJqHnz5plp06aZrVu3mvbt25uNGzdGXtO3b1/z+uuvmxdffNG+/scffzSdO3cOdLsBAAASKZ3wGQAAAGSsKVOmRP0+ZswY26NqwYIF5thjjzXr1683o0aNMuPHjzft2rWzrxk9erQ54IADbGLryCOPDGjLAQAA4qMnFQAAQAgoKSXVq1e3/ypZpd5Vxx9/fOQ1+++/v2nQoIGZO3duwvfZvHmz2bBhQ9QPAABAOpCkAgAAyHLbt283ffr0MUcffbRp1qyZfWz16tWmbNmyplq1alGvrVWrln2uoLmuqlatGvmpX79+yrcfAABASFIBAABkOc1N9emnn5rnnnuu2O81cOBA2yvL/axatSop2wgAALAjzEkFAACQxa688kozadIkM3v2bFOvXr3I47Vr1zZbtmwx69ati+pNpdX99Fwi5cqVsz8AAADpRk8qAACALOR5nk1QTZgwwbz99tumUaNGUc+3aNHClClTxsyYMSPy2LJly8zKlStNq1atAthiAACAgtGTCgAAIEuH+GnlvokTJ5pddtklMs+U5pGqUKGC/bd79+6mX79+djL1KlWqmN69e9sEFSv7AQCATESSCgAAIAuNHDnS/tumTZuox0ePHm0uvvhi+/9Dhw41JUuWNHl5eXbVvg4dOpgRI0YEsr0AAAA7QpIKAAAgS4f77Uj58uXN8OHD7Q8AAECmY04qAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACA7E5SjRw50hx44IGmSpUq9qdVq1bmzTffjDy/adMm06tXL1OjRg1TuXJlk5eXZ9asWRP1HitXrjQnn3yyqVixoqlZs6a57rrrzD///BP1mpkzZ5pDDz3UlCtXzuyzzz5mzJgxxdlsAAAAAAAAhClJVa9ePXP33XebBQsWmPnz55t27dqZ008/3SxdutQ+37dvX/P666+bF1980cyaNcv8+OOPpnPnzpG/37Ztm01QbdmyxcyZM8c8/fTTNgE1aNCgyGtWrFhhX9O2bVuzaNEi06dPH3PppZeaqVOnFmfTAQAAAAAAkEFKF+ePTz311Kjf77zzTtu7at68eTaBNWrUKDN+/HibvJLRo0ebAw44wD5/5JFHmrfeest89tlnZvr06aZWrVrm4IMPNrfffrsZMGCAGTx4sClbtqx59NFHTaNGjcwDDzxg30N//95775mhQ4eaDh06JNy2zZs32x9nw4YNxdlVAAAAAAAAZMOcVOoV9dxzz5mNGzfaYX/qXbV161Zz/PHHR16z//77mwYNGpi5c+fa3/Vv8+bNbYLKUeJJCSXXG0uv8b+He417j0SGDBliqlatGvmpX79+snYVAAAAAAAAmZakWrJkiZ1vSvNFXXHFFWbChAmmSZMmZvXq1bYnVLVq1aJer4SUnhP9609QuefdcwW9Romsv//+O+F2DRw40Kxfvz7ys2rVquLuKgAAAAAAADJxuJ/st99+dq4oJYJeeukl07VrVzv/VNCUNNMPAAAAAAAAciBJpd5SWnFPWrRoYT766CMzbNgwc84559gJ0detWxfVm0qr+9WuXdv+v/798MMPo97Prf7nf03sioD6XasJVqhQobibDwAAAAAAgDDNSeVs377dTliuhFWZMmXMjBkzIs8tW7bMrFy50s5ZJfpXwwXXrl0bec20adNsAkpDBt1r/O/hXuPeAwAAAAAAADnek0rzPnXs2NFOhv7HH3/Ylfxmzpxppk6daicr7969u+nXr5+pXr26TTz17t3bJpe0sp+0b9/eJqMuuugic++999r5p2666SbTq1evyFA9zXP1yCOPmP79+5tu3bqZt99+27zwwgtm8uTJyTkCAAAAAAAAyO4klXpAdenSxfz00082KXXggQfaBNUJJ5xgnx86dKgpWbKkycvLs72rtCrfiBEjIn9fqlQpM2nSJNOzZ0+bvKpUqZKd0+q2226LvKZRo0Y2IdW3b187jLBevXrmySeftO8FAAAAAACAcChWkmrUqFEFPl++fHkzfPhw+5NIw4YNzRtvvFHg+7Rp08YsXLhwp7cTAAAAAAAAOTYnFQAAAAAAAFBUJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAgCw1e/Zsc+qpp5q6deuaEiVKmFdffTXq+Ysvvtg+7v858cQTA9teAACAgpCkAgAAyFIbN240Bx10UIGL1CgppZWY3c+zzz6b1m0EAABIy+p+AAAACE7Hjh3tT0HKlStnateuXej33Lx5s/1xNmzYUKxtBAAAKCx6UgEAAITYzJkzTc2aNc1+++1nevbsaX799dcCXz9kyBBTtWrVyE/9+vXTtq0AACC3kaQCAAAIKQ31Gzt2rJkxY4a55557zKxZs2zPq23btiX8m4EDB5r169dHflatWpXWbQYAALmL4X4AAAAhde6550b+v3nz5ubAAw80e++9t+1dddxxxyUcHqgfAACAdKMnFQAAQI7Ya6+9zG677WaWL18e9KYAAADkQ5IKAAAgR3z//fd2Tqo6deoEvSkAAAD5MNwPAAAgS/35559RvaJWrFhhFi1aZKpXr25/br31VpOXl2dX9/v6669N//79zT777GM6dOgQ6HYDAADEQ5IKAAAgS82fP9+0bds28nu/fv3sv127djUjR440ixcvNk8//bRZt26dqVu3rmnfvr25/fbbmXMKAABkJJJUAAAAWapNmzbG87yEz0+dOjWt2wMAAFAczEkFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAA2Z2kGjJkiDn88MPNLrvsYmrWrGk6depkli1bFvWaTZs2mV69epkaNWqYypUrm7y8PLNmzZqo16xcudKcfPLJpmLFivZ9rrvuOvPPP/9EvWbmzJnm0EMPNeXKlTP77LOPGTNmTHE2HQAAAAAAAGFJUs2aNcsmoObNm2emTZtmtm7datq3b282btwYeU3fvn3N66+/bl588UX7+h9//NF07tw58vy2bdtsgmrLli1mzpw55umnn7YJqEGDBkVes2LFCvuatm3bmkWLFpk+ffqYSy+91EydOrU4mw8AAAAAAIAMUbo4fzxlypSo35VcUk+oBQsWmGOPPdasX7/ejBo1yowfP960a9fOvmb06NHmgAMOsImtI4880rz11lvms88+M9OnTze1atUyBx98sLn99tvNgAEDzODBg03ZsmXNo48+aho1amQeeOAB+x76+/fee88MHTrUdOjQIe62bd682f44GzZsKM6uAgAAAAAAIFvmpFJSSqpXr27/VbJKvauOP/74yGv2339/06BBAzN37lz7u/5t3ry5TVA5SjwpqbR06dLIa/zv4V7j3iPRUMSqVatGfurXr5/MXQUAAAAAAEAmJqm2b99uh+EdffTRplmzZvax1atX255Q1apVi3qtElJ6zr3Gn6Byz7vnCnqNEll///133O0ZOHCgTZq5n1WrViVrVwEAAAAAAJBJw/38NDfVp59+aofhZQJNsK4fAAAAAAAA5EhPqiuvvNJMmjTJvPPOO6ZevXqRx2vXrm0nRF+3bl3U67W6n55zr4ld7c/9vqPXVKlSxVSoUCEZuwAAAAAAAIBsTVJ5nmcTVBMmTDBvv/22ndzcr0WLFqZMmTJmxowZkceWLVtmVq5caVq1amV/179Lliwxa9eujbxGKwUqAdWkSZPIa/zv4V7j3gMAAAAAAAA5PNxPQ/y0ct/EiRPNLrvsEplDShOVq4eT/u3evbvp16+fnUxdiafevXvb5JJW9pP27dvbZNRFF11k7r33XvseN910k31vN1zviiuuMI888ojp37+/6datm02IvfDCC2by5MnJOAYAAAAAAADI5p5UI0eOtJOSt2nTxtSpUyfy8/zzz0deM3ToUHPKKaeYvLw8c+yxx9qhe6+88krk+VKlStmhgvpXyasLL7zQdOnSxdx2222R16iHlhJS6j110EEHmQceeMA8+eSTdoU/AACAXDV79mxz6qmnmrp165oSJUqYV199NV+v90GDBtn4TA2IWi35q6++Cmx7AQAAUtaTSoHPjpQvX94MHz7c/iTSsGFD88YbbxT4PkqELVy4cKe2EwAAIIw2btxoG/DU07xz5875nlcv9Ycfftg8/fTTttHv5ptvto18n332mY3RAAAAQrm6HwAAANKrY8eO9idRY+JDDz1kp1E4/fTT7WNjx441tWrVsj2uzj333DRvLQAAQBpW9wMAAEBmWbFihZ3rU0P8HM0X2rJlSzN37tyEf7d582azYcOGqB8AAIB0IEkFAAAQQm5BG/Wc8tPv7rl4hgwZYpNZ7qd+/fop31YAAAAhSQUAAICIgQMH2oVx3M+qVauC3iQAAJAjSFIBAACEkFZUljVr1kQ9rt/dc/GUK1fOVKlSJeoHAAAgHUhSAQAAhJBW81MyasaMGZHHNL/UBx98YFq1ahXotgEAAMTD6n4AAABZ6s8//zTLly+Pmix90aJFpnr16qZBgwamT58+5o477jCNGze2Saubb77Z1K1b13Tq1CnQ7QYAAIiHJBUAAECWmj9/vmnbtm3k9379+tl/u3btasaMGWP69+9vNm7caHr06GHWrVtnWrdubaZMmWLKly8f4FYDAADER5IKAAAgS7Vp08Z4npfw+RIlSpjbbrvN/gAAAGQ65qQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAGR/kmr27Nnm1FNPNXXr1jUlSpQwr776atTznueZQYMGmTp16pgKFSqY448/3nz11VdRr/ntt9/MBRdcYKpUqWKqVatmunfvbv7888+o1yxevNgcc8wxpnz58qZ+/frm3nvvLe6mAwAAAAAAICxJqo0bN5qDDjrIDB8+PO7zSiY9/PDD5tFHHzUffPCBqVSpkunQoYPZtGlT5DVKUC1dutRMmzbNTJo0ySa+evToEXl+w4YNpn379qZhw4ZmwYIF5r777jODBw82jz/+eHE3HwAAAAAAABmgdHHfoGPHjvYnHvWieuihh8xNN91kTj/9dPvY2LFjTa1atWyPq3PPPdd8/vnnZsqUKeajjz4yhx12mH3Nf/7zH3PSSSeZ+++/3/bQGjdunNmyZYt56qmnTNmyZU3Tpk3NokWLzIMPPhiVzPLbvHmz/fEnugAAAAAAAJCDc1KtWLHCrF692g7xc6pWrWpatmxp5s6da3/Xvxri5xJUoteXLFnS9rxyrzn22GNtgspRb6xly5aZ33//Pe5nDxkyxH6W+9EQQQAAAAAAAORgkkoJKlHPKT/97p7TvzVr1ox6vnTp0qZ69epRr4n3Hv7PiDVw4ECzfv36yM+qVauSuGcAAACZT9MjaM5Q/8/+++8f9GYBAACkZrhfpipXrpz9AQAAyGWaJmH69OlRjYEAAACZKKVRSu3ate2/a9assav7Ofr94IMPjrxm7dq1UX/3zz//2BX/3N/rX/2Nn/vdvQYAAAD5KSlFvAQAAEyuD/dr1KiRDYpmzJgRNYG55ppq1aqV/V3/rlu3zq7a57z99ttm+/btdu4q9xqt+Ld169bIa7QS4H777Wd23XXXVO4CAABAVvvqq6/sQjR77bWXXVF55cqVBb5eC88oXvP/AAAAZEWS6s8//7Qr7enHTZau/1cApHkP+vTpY+644w7z2muvmSVLlpguXbrYQKlTp0729QcccIA58cQTzWWXXWY+/PBD8/7775srr7zSrvyn18n5559vJ03v3r27Wbp0qXn++efNsGHDTL9+/Yq7+QAAAKGlBr8xY8bYlZRHjhxp47RjjjnG/PHHHwn/hsVnAABA1g73mz9/vmnbtm3kd5c46tq1qw2K+vfvbzZu3Gh69Ohhe0y1bt3aBkrly5eP/M24ceNsYuq4446zq/rl5eWZhx9+OPK8AqS33nrL9OrVy7Ro0cLstttuZtCgQfY9AQAAEF/Hjh0j/3/ggQfapFXDhg3NCy+8YBv/Ei0+428IVE8qElUAACArklRt2rQxnuclfF69qW677Tb7k4hW8hs/fnyBn6PA6t133y3WtgIAAOSyatWqmX333dcsX7484WtYfAYAAIRyTioAAABkDk3T8PXXX0ctaAMAAJApSFIBAACE1LXXXmtmzZplvv32WzNnzhxzxhlnmFKlSpnzzjsv6E0DAABI/nA/AAAAZKbvv//eJqR+/fVXs/vuu9u5QefNm2f/HwAAINOQpAIAAAip5557LuhNAAAAKDSG+wEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIBAAAAAAAgcCSpAAAAAAAAEDiSVAAAAAAAAAgcSSoAAAAAAAAEjiQVAAAAAAAAAkeSCgAAAAAAAIEjSQUAAAAAAIDAkaQCAAAAAABA4EhSAQAAAAAAIHAkqQAAAAAAABA4klQAAAAAAAAIHEkqAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBIUgEAAAAAACBwpYPeAKCoNvW7N+WfUf7B/in/DAAAgHQhfgIAZAN6UgEAAAAAACBwJKkAAAAAAAAQOJJUAAAAAAAACBxJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCVDnoDgFy1qd+9Kf+M8g/2N+kQln1hPzLz+gIAwI/6uvCIBXNzP4BsRk8qAAAAAAAABI4kFQAAAAAAAAJHkgoAAAAAAACBI0kFAAAAAACAwJGkAgAAAAAAQOBY3Q8AkLFYZQcAAKBoWOkZ2YyeVAAAAAAAAAgcSSoAAAAAAAAELuuSVMOHDzd77rmnKV++vGnZsqX58MMPg94kAACAjEb8BAAAskFWJamef/55069fP3PLLbeYjz/+2Bx00EGmQ4cOZu3atUFvGgAAQEYifgIAANkiqyZOf/DBB81ll11mLrnkEvv7o48+aiZPnmyeeuopc/3110e9dvPmzfbHWb9+vf13w4YNRfrMTZs3mVTbUsRt2hlh2Y8w7UtY9iNM+8J+5Oa+pGU/Bj5k0qH8kD6h2Jei7oer2z3PS9EWZbeixE/JiqHCcu+GaV/Csh9h2pew7EeY9oX9yM0YKizxU1H3JVPjpxJepm1RAlu2bDEVK1Y0L730kunUqVPk8a5du5p169aZiRMnRr1+8ODB5tZbbw1gSwEAQBBWrVpl6tWrF/RmZHX8JMRQAADkjlUZFj9lTU+qX375xWzbts3UqlUr6nH9/sUXX+R7/cCBA23Xdmf79u3mt99+MzVq1DAlSpRIyTYqE1m/fn17kqtUqWKyWVj2JSz7EaZ9Cct+hGlfwrIfYdoX9qNo1N72xx9/mLp166bsM7JVUeMnIYbaeWHZjzDtS1j2I0z7Epb9CNO+hGU/wrQvG9KwH5kaP2VNkqqoypUrZ3/8qlWrlpbP1kWUzTdEGPclLPsRpn0Jy36EaV/Csh9h2hf2o/CqVq2a0vfPJcRQxReW/QjTvoRlP8K0L2HZjzDtS1j2I0z7UiXF+5GJ8VPWTJy+2267mVKlSpk1a9ZEPa7fa9euHdh2AQAAZCriJwAAkE2yJklVtmxZ06JFCzNjxoyo7uf6vVWrVoFuGwAAQCYifgIAANkkq4b7aX4ETfR52GGHmSOOOMI89NBDZuPGjZHVaoKmrvFa3jm2i3w2Csu+hGU/wrQvYdmPMO1LWPYjTPvCfiCX4qcwXSth2Y8w7UtY9iNM+xKW/QjTvoRlP8K0L+VCsh+hXt3PeeSRR8x9991nVq9ebQ4++GDz8MMPm5YtWwa9WQAAABmL+AkAAGSDrEtSAQAAAAAAIHyyZk4qAAAAAAAAhBdJKgAAAAAAAASOJBUAAAAAAAACR5IKAAAAAAAAgSNJBQAAAAAAgMCRpAIAAAAAAEDgSFIhKTzPi/v/6f78oLcDANItU8u6bCuPs2EbEU5B3yvEUAByUaaWc16WlcWp2MYSXjbsOTLa9u3bTcmSJTPi83/77TdTtmxZU65cOVOmTBl705QoUcLkgqDPw+LFi813331nj/2+++5r9txzT5ML3n33XbNs2TLzww8/mHPPPdfstdde9trL9Gsi6OtlZ2TjNqfzmOj/t27dau/BoGXTufrxxx9N3bp17f/nUp2BzBD0vUIMlRnnIRdjKOKn9MrW7U4V4qfMjp9IUiFpN9KIESPMnDlz7E3esmVL07dv35QHN/4b4p577jFvvvmm2bhxo6lUqZIZNWqU2XvvvU2unYc33njDfPPNN6ZatWqmadOm5pBDDkn5548ePdoMHjzYVKlSxaxdu9YcddRR9veDDjrIhNlTTz1lbrjhBtOsWTPzySefmN12281ed9r/oCsZ/+ePGTPGfPrpp/b3ww8/3Jx11ln5XpPp/Nv62GOPmeXLl9vgtnfv3vY6d5VkLvEfk/vvv98sXLjQfPzxx+byyy83rVq1suVwEPzl8vDhw828efNseXTooYeaSy65xGSSZ5991txxxx3mpptuMuedd559LJe+mCNYxFCZgRgq/Yif0osYKhrxUxbET0pSAcU1YMAAb/fdd/cuv/xyr2vXrl65cuW8008/3Vu5cmVaPv/GG2+0n//MM8947733nrfXXnt5BxxwgPfzzz97ueS6667zGjZs6LVp08Y7+eST7TGZPHlySj/z5Zdf9nbddVdv/Pjx3rp167w33njDO+yww7wnn3zSC7PXXnvNq1GjhvfKK694W7dutY8deeSR3hlnnOFl2jWh7bzwwgu9gw8+2GvSpIl33nnnRZ7fvn27l+n++eefqP2pXbu2/ffiiy+217j+/88///Ry1fXXX2+Pw4gRI7wHHnjAa9y4sXfiiSd6a9euTfu2+K+nQYMGebvssot3wQUX2PKoTJky3qWXXho5n0Ffe1OmTPFq1qxp74m2bdt6zz33XOS5oLcNuYUYKjMQQ6UH8VN6EUMlRvyUufETSSoU24IFC7x69ep577zzTuSxpUuXerVq1fLOPffclH/+qlWrvCOOOMLeMPL666971apVswWOX9A3dKqNGzfOq1Onjjd37lz7+/Dhw70SJUrYx1Plp59+8k477TTvtttui3pcBelxxx0X2mP+yy+/2Mpdlci2bdu8zZs328d1rA899NCM2e+ZM2fae/Pdd9+1v//999/e2LFjvWbNmnndu3f3Ml2PHj28P/74I/L71KlTvT333NNbuHCh/V3Xuq5xf+WYaz766CP7ZdLd9zrXCmZ0nkXXZxA+/PBDr0uXLvYalC1bttgyumrVqt6VV17pBW3jxo02GaBr7K233vLOOeccr3Xr1lHXUlDHDrmFGCozEEOlB/FT+hBDFYz4KbPjJ5JUKLZZs2bZgvzHH3+0v7tWkXnz5nkVKlTwJk2alNLPX7x4sbfbbrvZz1ULVOXKlb2RI0fa59Qy8NBDD3mbNm3ywsZV5K4guOGGG7wrrrjC/r9ap3QcHn/8cfu7Kqlvvvkm6dvw/fffe1dddVWkEnfbomN+zDHHeGGzaNEi+6+utb59+3qvvvpq1PNqcVWLzO+//56vgE5H4OU+w/2rVvH69evb7XF0LTz88MM2GFy+fLmXqRS4K3jw37svvPCCd/zxx9v/V6uzWpncFyntlwIvf4thGMXun8rZgw46yP7/888/n6/80zXpP//p8NJLL9nra9999/VWrFgRdU2++OKL9gvw7NmzvaC4bdH18uabb9r///jjj72zzz47X6CVKV+YEF7EUMEghkov4qf0IobKj/gpu+Kn7BlMi4ylccw///yznQBRSpcubcekagLEevXqmd9//z2lqx1ogsnDDjvMXHvttebss882Dz74oLniiivscytXrrTzC8ydO9eEbSy1G/O7adMm+2+pUqXsmP7XXnvNdOnSxdx3333msssus8fp9ddfN88//7ydayIZtm3bZv/dY489zEUXXWRat24d9XyDBg1M+fLl800Kms0eeughc8IJJ5ipU6faa1zzd5x++umR8yFVq1a1c0po391Y98mTJ5u///47LXPcuM/48ssv7b8NGza02+I/9pUrVzYdO3Y0S5YsMZ9//rnJRJr/QXNUaH4KTWKpe1jztGzYsMH+vPPOO/Yev/vuu03Pnj0jx/nxxx9PanmTaXTf6T6X2bNn2/tZx0XX1zPPPGN69Ohhj4kr/zSXgR5fs2ZNWrezQoUKpkaNGnZel/nz50ddm5rfRfPd/PrrrybosrNx48bmxBNPjGzXgAEDbJn2yCOP2PJSfvnlFzNlypRAthW5gRgq/Yih0ov4Kb2IofIjfsrC+KmYCTXkkERd9/766y/b5U/jydUK5ygLrW6xao1I9ucrO+u6CKuVQF2jy5Yt6/373/+O2q6TTjrJ69ixY6iGbegYL1u2zP5///797bGXRx991M5rULFixUhLgGiOg/bt23sDBw5M+rbcdNNN3pAhQ/KNZX/qqae8Qw45JPK7xlNrfo1sppYLjQ0/8MADI60HrmXGtRZ88MEH3v777x95rl27dvb6S2dvjMcee8xe9/Ldd995TZs29c4//3zvyy+/jGq91fwK/uElmULdmjds2OAde+yxtltzt27dbDny22+/2R8df3VP13466oZ/yimn2O7RYe35omuuVatW9v/VCt2iRYtIC5/2Xcdk2LBhUcdE913nzp1TWv4lem/1DlGLrYYR+ed0UXm09957e//973+9IKnXxN13322vtdihV+q6rutPvSiOOuoo25qu6yqs1xbSgxgqMxBDpR/xU/oQQ+VH/JSd8RNJKhT5RlJXvgcffNC75ZZbvPXr10fGz6qrn7on3nrrrXbCxxNOOMEWhsnuOqoKXYWKCpn777/fjulXN1WN31cXSRWy2jZ1lW7evLktsGP3IVup4NQ+aky5xgNXqVLFdtV3XKCpIOyLL76wPx06dLDHyg0hKA5/ITNt2jQ7Z4bGdMdS5aeJP3XMVdDvs88+kfOQzRREKWBRha+x/bHHRUFL3bp17ZwLCnR0P7j9TlfFr3tTx9s/ZERDOTp16mS7qU+fPt0G3LqOMq1bt8oLN9xi1KhR3h577GGvZ415d1+mVDkrcFTAri7GEyZMsJNc6py4fQ5bkKX7SPe0ricFKOru/fXXX0eeV7fro48+2k74O3r0aG/o0KG2/NVxcsckFeWf/zirXtA5U3d0R/eI7n9NwqxyW+XCqaeear+IpPvaK2zZ5Y6nJvBV4KqyM933MMKHGCozEEMFh/gp9Yih8iN+yt74iSQVirwCjSoRVdq6gTXeWRlfWbJkiQ2u9LzGpSoD7S7O4txQ/sJB71+9enXv2muvtS0Eqjz0OZpkVFnxO+64w2af9dg111wTKWCSEVxkEh0DzVXhxvS7/dNxPvPMM+38Fhpr3rJlSxtoJuM8+KmwvOeee7zBgwfHLXy0cotawRT0+oOrbD0P/v1T0BIv0JL333/fBr9qPUjHfscr9N9++21bqflbZvUFSAGJKmEFMbp/k31NJGOFFX0hcu688057DesxtXy5Va7UkqQKXZWf7gP9m5eXl3H7kwpqiVbFr9a12DJSX6a0ApFWWvnXv/5lv2yl677TudO50Ko4+vFP9qygXmVB+fLlbXCv1kp/eZVuOyq73BdZXXeHH354aOsQBIMYKjMQQ6UP8VN6EEMVjPgp++InklQotP/85z82K6/Mu+s+qRtemWndSI5a5HSRuos3WTe4Mt/qGq0srqPgTgWOuheq63q8GyZMBa72Zc2aNbb7pApTZdQVXIp/39VipfOjc+UC1GSdBxXcCtx07rUUb7xjruEJel7d1bM5uErUeqJgKl6gpS7t2m99wUjnfmuYgpbO1b0xY8YMe324VUH896W6emsixmTfm8nqPqxlv11Zo6XYv/rqK1sp6x7v1auXXYVK3PYrsNA+ZeL+JHsiV937zz77rB2GogBAgZR73l/G6Tz7j0Mqjom7L/T5+mKrXhnqjaAeGZqMVUG+HnN0TaoFUBO5uhZd/zCPdClM2aXjpWBW+5DNZRcyDzFU8Iih0of4Kb2IoaIRP2V//ESSCoWiG0kFoLpCupVPtBTmE0884bVt29a2eqiAV2Dll6ybSGNydWNovgC3TLJ7b1Umrnt2GMXOI+EKKN34qnj2228/77PPPov6G/+Ss7HvUVTxzqFaYtTSqlZYt3RrbBCilohsboX1HzONs1YLoFu2V957771IoOUqDs3hoZV60rXfOjcKMlShqQVGFUOjRo3svaLx95pfRF2Itd1z5sxJuH9B07aoC7q6XOua1v38ww8/RJ5Xy40qRwVZ/sf9+5BJ+5MM/v1RuepW6NE5V1mnHhgKtPy0dLwbPuRem8rt0twcCq50/emLn9vWl19+2V6HCqwcbbNep9bAVK9WVpyySzT/SDaXXcg8xFDBIYZKP+Kn9CKGikb8FI74iSQV4opXWGmJXGV61eqksb1ukjkFPCrUNc430RjV4tINrK7nJUuWjExoqYvf3USaxPCuu+7ywsZfSGjpWO2jCtKff/45UmBoslUVuFreV12UNa+FjlXs3xf3OnAtSY4CCrXaqAv0J598kvA9si24ij1uN954o21xrVmzpu2GrmES/kBLrQZawlZfOtKx34kCCQXdOv/6wqGgW0MWNK+Dutxq3g21wmTaPAPqOuyWXRfNwVGuXDnvoosuyjf/hoIsXeu9e/eOtAbmgttvv90Ov1GXfLe0r861Wvl1XeqYaJiQ5lBQAJOuc6xJhHXv6/pSQLV27drIcwq0dD8o8Nc942jOEU2oqfk9Nm7cmNLt29myy/93YepBgvQihsoMxFDpR/yUPsRQBSN+yu74iSQV8vFfZCoAx40bF/W8ukyqlcEVcmqh0zwLV199dVIuykSVyOrVq72ePXvaFgIFGf4bSK2Q6t4aJv7CUgVa5cqVbQuJgkytRjN//nz7nDL/aj3R2HONl1cFm4wJNv2f7wp6BRqalNEV9mqdUGuECln/5KNhof3WPitw0fWnyl1fJtxqQC7Q0go0Gs8uqazk/PeGKqyxY8faFpZvvvkm6nVqoVSQ4mglo0xrJdMXBl1T2i4dM32JUquqjq0q5n79+nm//vpr1N/cd999trVTXfPDyn+etL+aoFJDdHR96d7XnDEK4FXW6rrUcJAGDRpEDZFIxTXoL9tVB+gztQKVtkdDmHQu/RRoqe5QsO8vjzS8yM2NkSqUXQgSMVRmIIYKFvFTahFD5Uf85IWq3CJJhYSuu+46e/No8j3X6iT33nuvvfE///xzW/Fo9QEVAk6yJvjUzaixuK4rsKjAveyyy7wyZcrYYEs3kLpA+ldhCBvNiaBK3HWt1ISaavXTCjz+VtdHHnnELvmZ7K7SN998s53Ub/jw4bYlRquAqFVCn+ey6irENLHf8uXLvWylYMV1CRYVvCqE3fwdau1WkKsVgXQ8/Et167XpDGJ0b+pLhQJqtQAp6PBXFLpnYyu9TOwZ4rZH97m/ZUjXnLqmqzU7NshSxZ1p+5EKCow1ybG//NPcGQryb7vttsj9rQBGwymSPW9KIlp9Rqscqaxxn6cvGppoWC2Rfm6Je7ed6ZYrZRcyEzFUZiCGSj3ip2AQQ8VH/BSOcoskFeIaM2aMt/vuu9ubN5ZuHE08pwrHrXaR7FYnzd2gVT40qaW6wGucuiuA1e3QtchoxQ2tzuIqx7AFWSoczjrrLLvyhr/Amjhxog2yVOFrgs9YyaiAdD40SaRaGtwKOK7w10ohapFU4epaInVOsrXi05Kz6ibtvwa1Lw899JC93hTs16lTx1YsutY1HEDXn/71S0egpTlM9AXHBdz6wqNt0Th2/wo12t5M7dLtLy80F4hauNSTwL8s8KBBg2yQpeEBsUGWZOu1Vhhq4XPzx8TOP6BrUMdLXy6TOW9KIprQ2XXp1r2goEXbpgDG0X2jskATzurLYNByqexCZiKGygzEUKlH/JR+xFCJET+Fp9wiSYWES2JecsklUUFL7OSTmgD0+eefT3qrk+YMUCXiJipUd0jd1Noetyysxu8q6NLYa9dS4w9AwkIZ94oVK9oulW4FGketgQp0NVZeLbLJoPkZVOgoQy6aP6N27dp2QkY/FVZqiXJzW4Sh4nPXryqUDRs2RF3zuta0UooL5DXPgloVFPymqwXQfY6WDXet7qpA9EXHtcqo0tW9oVVy1EKYiV3U/YYMGWInFFbLjL6s6bj6u97fcsstdliMJpD1T2iZC3RsVO6pBSu2+/mTTz5pn3OTMKfyC56GwPh7G6jFTMHLoYceGtXtXNuoMls9NPr06eOlWy6XXcg8xFCZgRgqPYifgkEMFR/xUzjKLZJUiOuMM86IWvnAFdYKYuLN6J+si1OFqyoul73Vv2oFVPZWN3u3bt0iBa3rtq5KRnM6ZLtEFeLTTz9txwNrvgp/K4kowNXEk8moTDVRn95LK0qosHRLNauFTJW6Wm78hb2CDI1Rznb+FilNbKvKS2PFXWGtfW7Xrl1kpQ0FWmqVVUXnpCqY+fTTT/ON99Z8A/fff7+dU0TXvqsstA2qdFUxuu1O5bbtDP/1o2NcoUKFyBcptbpqaExskKV7X/d5Jk5amgwFTTSpY1GqVCnbKyOWvmClsteD5tJRwORvYfYHWvrSpzrCP2mrzpEmIU33l6xcLbuQuYih0o8YKv2In9KLGCoa8ZMX6nKLJFWOS1RoqaBWC5MCF/9r1P1VY1DVnTIV1B1dBYqyurpZNLmcG/+qrqyqABX8uWWa1X3yvPPOs62GqV7tIF0FrVo1NW7ZX7Cqi3LdunXt+PLYSR7jvUdRaZldLSmqY63CO/ZaUPdYbYM7xmpxUgGm1ops5r+2XauKWjEqVapkj7/bXwUDGjPeoUMHuxqI7g1XkaSq4h8/frztVqsWMLW4uM/ROHt1C9aKM/7WjF9++cWOadd4+3j7l0nUBVpfGGInFHZBlipGdTeO3Y9M3Z+d5b9nR4wYYb9A6gvm3XffHQn+dSxKly4dN9CSVARaallWgBW74pK6zrthAy7Q0iovanWLla5AK1fLLmQGYqjMQAyVfsRPwSGGIn5Khkwvt0hS5TD/Df7dd9/ZzKn/BtKFqAk9n3nmGRvQfPnllzbTqq6jxb2BVFAmeg/XJVhjdrWygRs3rIJHLTDK4vr/Vl1d/dnobNa/f3+77Ki6gmpJaM0p8dVXX9nnVFCootdrdC6SOeGlxm6rUvfzH2MNF1CLhM6Hhgy0bds26yda9VfWDz74oFejRo3I2HGNsdYwCFVsuk8UwCjQOuecc2x3cVcBpqoi0WcpiFLF+8UXX+R7XsdfY9u1OpGu/W+//dbeF1rONtPPiVbVUZCqitFViv5hJlqWXV+sdA5iW5nCSve0Wvp1n2kVKn2Z8i95rbkldD26Vt5UUguzvshqmIyf5krQ8sduKIerJ9T1W2VBvDkvUi1Xyy5kBmKozEMMlR7ET8EhhopG/BTecoskVY7yB1e6CFWhaxJJFdJuWV5NxnfSSSfZCl/dYjW5p54vbgWjgMhPy3FqDoUJEyZEMsr6jE6dOkVWO1DXYRU6Cvayedy+X2yFoZUnVOm4468u6irs/N3wlaFXgeFaRot7DehHq6xovgDXsppoG9VtVV08NdGqWstcIZXt50HjxVX4xrYiaB4FLdXtAq1YqSqk1fqtStYt9ernKjgFfZoQU3MQKDhU66R+Uh387QwFgApe3ZwfmhNFQYNa7tWi775Q+YcNaHUdXWdhDar8+6WhP1ry3HXZVznonyfDUcB5zDHHpPyY6HO12pGGCLg5E/TFVuW/64Hg3wZ9CejcuXNarznKLgSNGCp4xFDBI35KPWKoaMRPuVNukaTKceoKqQz0Sy+9ZMeTa4ys5i9Qpl40iaBuIlX2WvHCXZQ7W8FoxRkFSi6Q0qoTWgFHmdl9993XdtV0LR/K8iqYUOXRpEkT23KQDa0chR0rH0vd0F0XSp0PtQRpXLOo676jOSaSVTiooNKx1XlI9LzofMUr3LP9fOg467pSt1sX2PpbpBRoadJVtci5ORYklRWdKjkNB/EHHBq+oGWTDzroINsS6e4fLeusLyhaPae492YqaL4PdfHXKj7+OSjUdVgTemoCSV338YKsMHZP17wQsUNNdD+r/BN1D9e8MW4YgnpAKOiKPRapPib6fH3p1nACTSCr684NHfB/dux8H+kOtHK57EJmIIYKBjFU8IifUo8Y6v8RPyVPtpRbJKlymFaw0JhtFdCilhAFVyr0NBlfojkTinMjqbWvdevW3sUXX2yXsFSGWUs06ybQKgKqXFQguxYDVS7K9qowDkOrk2hfFDSK/+bXsAB1VdU+q6BVxe5eo276muzRLxnHQYGDlqh2BVW8wkitTxdeeKG3aNGiqMfDUPGpAFblr+tdkyy6ffIHWl26dLHjxtO1v//5z3/sOXErEalFRt2EW7RoYQOsqlWrRiYhjZVJ94bGuqs8GTt2bNQXCndvK4DQMXdLJLtj7q/8wnCNOe+//74ty2KXmlc5rBYqfYlVC6B6Azj6Uqs5NdQtPB3HxN/ira7x+tKrlmZtR+zzp5xySmQJ8SDOU66XXQgeMVQwiKEyA/FTahFD/T/ip+TKlnKLJFWOUmGs1jY3SeDUqVNta6BurB9++MF2W1e3abc0cTJprK7Gteom1Y+/q+ELL7zgHXfccXZ8uCuI/RVHprVy7AxNVOoK2u+//z7yuOZLUOBVvnz5qAkd9XoFX/4JHZPBFTS9evXy6tSpYysBx3/MdZ1oyEBs1j/bxHY5d+fg559/thPHKojxdxH2B1rub9NROKvS1XWge1Dd0fWjilcT7oruSQ1hWLhwoZep1EKpytk/tER0nLXtGrfvKkH1RDjqqKNsK1lsABI2bv80Z4a7n3ReNU+KjosmPXVULqoc1DFLZ1Dgv08UJLvWZ1cea1s0hGnvvfcO7HzlWtmFzEMMFRxiqPQjfkovYqj8iJ9yr9wiSZUjVHC78di33357ZLy2xmaL5i5Ql1F3kSozrYtXLSDJoFYAf6Wgif00Rldjx2MnjFOQpRtDk45qLHZYKHj1d3lW11QVrK6A0ESeavHRcdEqECrA9JgKNB2LVAWXmq/BrbwSuzS2Wmo05l3XQ6YtxVsU/m1XIKtu6GrF0ApAomtQvx999NH2eVeI+495OvdfAYquF61Eo23zB3nq3qyA0B+cZwo31l2Vn1qw/KtFadJUDQs499xz7WSN/iBL3aLDukSyWqI0saezbNkyO3Gy7mktN+zm9VAroK5BrZKkYRT6otmsWbPINRhUoKXrUD1DFGhp21VXqIXQBVhBfunNhbILmYEYKnjEUMEgfkofYqhoxE+pkw3lFkmqHKCxsCqU1ZKk7pKao8Dd3KJCXCsMqHJxF6e6kGt+hWTc2P6lYP3jiTVXgMbEXnDBBbbl0U9dN1XoZmulHkvdJRVM9ejRIxJkrV692haq6n6sbvvudaroNRGgWlE0yapaSFI9oaO6SKsVWOdDLZCahFBdjDW/hgI+9/nZfj40L4EKZU0CqGEBOieudVVzh+h8KMj1t8ik047uN807oPlINO9IpgYjukZiuxHri4ImXNR9rlWwNIml5qrQFwm3X2GbP8Hfyqku+/4lezUsSOWxWnxdWazyVuWkJt1UGaBANMiJXGOXd1ZQqP3Quc2EACvXyi4EhxgqeMRQwSN+Sg9iqP8hfkq9TC+3SFLlAF1canHShaix47qZxd/V8Pzzz7eTb2rMvm5w3ezuxi7OxRlvKVj/+6k1UAFE165dEy6BnM2Vup+GA1SqVMkGmm6SQ9dVWo+7IEstPGoZUIuAHkvGhI6JKi3/sR03bpwt+HWNqLu8ClW1YrjPzYQCtTjUzVvL8n744Yf2d7W+KsjSPB6OlhBXC4yCsFRX9IV5f/careai7dUytmodCrriKIha/tRSpIDWvw/umhfNF6HXKADxC0tw5ad7/N5777XloHpgOGoFVXd0f6C1fv16ew1q9a54rdHpFltW64t3ussDyi4EjRgqMxBDBYf4KX2Iof4f8VPxZHu5RZIq5NyFqK7qyjBrJQ51N3Tdw11hreBHExwqsNKNlIxCvKClYNXS6O8OqSBLy9i6ceNhokLCzRmhbshq/fAHWWqBckGWf2ywX3FaAvzn0G2Hv+DyP69C/quvvvI++OCDqFUdsjG4ij1mWsVFBbEbDqHuwW7eClVqbqJNzV+R6jkUdnROYq8dtYirS65aATOpFSaW2wf1Nog31t3tt5bmVTCrpW3Dyn/9qbzVxL2JAi11X4+3WlUmBJzpXD68oM/OpbILmYMYKnjEUOlH/BQMYqj/IX5K7mdna7lFkiqkYm8MdZtU12hVLso8a7JNVSZ+uhjV4pCsi3NHS8FqzLULqNTlUK0Cd9xxhxcmCqg0nlqribh9VWtgvCBLLbHqtu5WCkr2dXDffffZIFYBRaLXxJMJBX1R+SeS1bF1QZZaCNTy51qmHX0J0Nhr3SNOqlrZduacaHUQXUvu8aArjuKMdddS4Ap227Vrl3Gr6SSL//x9/fXX9j7Xl0oFWrrH/YGWuuvreGgizVTPH+Pfrtj7uqBzEXu9peO85WrZhcxADJUZiKHSj/gpeLkcQxE/Fd+2kJRbJKlCyH/hqTJX4Txv3jz7u4IdVTLKPKtScRetJtzThILJvDgLsxSstsHRJIxhKnC1soO6R2vyRn+X6ERBlrq1qlVAQWmyKajdY489bGChQj/2PKulQucrDN566y17zEXdztXCrGOsVgJV6mXKlLHLeDua30ItbBoukc5CuTDnRF2EY2VqF/V4K1BpeEzTpk3t6jpqEdTQFY1193e5D9M9H3t+br75Zjtpr+ZQcAH/Pffcky/QUtnXr1+/lB4L/3ZpLpvu3bvb+V3811i8z/ffE5qrIN0TMedS2YXMQAyVGYih0o/4KXPkYgxF/JRc12V5uUWSKsR0carFQ13Uy5UrF7mZlNXVsqYKsjTZpyoeXcTJbl0o7FKw8+fPj/q7MBS4mr9C3aGff/75hK9xQZaCWxdkKeBNdiWqll91G1Y3Tv/ywBq77Wg7S5cubTPu2UwFr4L3gw8+2AbzmjjVLf0qKoxVuWu89TvvvONNnDjRBrWaIDCdq4AU5ZxoPH4mKuxYd80DoXHuKoPUEqtgNhPGuqea5orQEvQKsFxrtPsipUBLk2jeeeed+f4u1eWfeiVou/QFRK20e+65p3fCCSfE/Xz/OVavDpXXbqLWdMilsguZhxgqOMRQ6Uf8lF7EUIkRPxVfGMotklQh4r8h1OqhymTBggV2rO7QoUO9kiVLRrqC60ZSl+jrr7/ejtd2BV2yb/BsXgp2Z2i/NOmhuqYPGDBgh69XkKVA7Mwzz4zq0p/MIEutXmqNkE8++cR74IEHbNCrAESFvaibtloqwxDcyjHHHGMrBAVcscdSK88osFKLoL5kaH6RdLdIZfs5KcpYd3VN18o06omgfcqUse6p9Nlnn9nz+eabb0Y97vZdy9YreNY1OmbMmLRtl85B/fr17RcMdw40FELLWqs1PN62ir4Y68u6vjimU7bfJ8guxFDBI4YKHvFT6hFDJUb8lBx3heA+IUkVQrowtTypliv10ySHCrLiZZ8lmRdpWJaC3RnK+quLrsbwx+MqHwViokBTLbHJCKriHcuXXnrJFuYak6w5K7Q0q1rEtKSrrgd/F1DJ1MKqMBQsqULv2bOnd9FFF9lJbLVEsh6LPU6a6FaTBaa6wg/bOQnLWPdU0jLotWvXtoFBLLVkKSjVNangIJWBZux5mDFjhl2BTK2Rjj5/woQJdsl2t2qZ/+9cgKVrNpXCdp8gexFDBYsYKhjET+lBDFUw4qei2x7C+0RIUoWMbuBrrrnGXpjt27fP97xumLJly3oDBw5M2meGeSnYnaGVEdQVVcsfJ6LsteaS+OGHH6IeL85xiG150bF1x1fjkbUSiLqcfvPNN/Yx/auhBGoxzmYFHTN90VBLs8av+wOt2JbndEzyGbZzku1j3VNJvS/UhVpBjfgDKfW+0ESz/nIz1S2iuv41j8KyZcvsHC/qBu733Xffebvuumu+VcTUaq7HUx1ghfk+QXYhhgoeMVT6ED8FhxgqPuKnotkW4vuEJFWWi1c5qHVDN1WirpD333+/17p166Rk4sO8FOzOUsWiLqFaEtm19MUeF3UXVff02NaTneV/b2XJNUZaq/9oSWy1SvjPj86Z/l9BbrJaH4Pi33ZN+KmCePr06VEV/lVXXWVbBG+88UZbOGufNZQg1cJ8TsIw1j3VtNJU48aNvYULF0YdI10HsT00ki12TgutEvTRRx/Z86OhGmeccYad78ZfZmkeEn93dM17o8fGjx+f0m0N832CzEcMlXmIodKD+Ck4xFAFI34qnLDfJySpspj/AtMYXnU1VAuHbmTReH4FWZrgM9GFXZwgK1eWgi0Kdzw1LEDHXkumah/9x1mFRF5enj1eye6yq0BC442VPdcEf1omu1KlSpFuqFrGVdeDVgc55JBDsroV1n/stK9qkVKloDHXGgIxc+bMyPNuyW5VNocffnjkHkmHMJ6TMIx1TzWdX83Xoe7h+lKruWy06lTz5s3TVu7pi5xWBPOvQKNgS9eZghlNTqrASi1tuj/850rXoX9J8VQL432CzEYMlXmIodKD+ClYxFAFI34qmrDeJySpspS/glG3c924WnGgbdu2dtyp6/KnC7dUqVI2G1zQexRHLiwFW1Sa2K9Xr142yNLQgffee8+2CGo1HrWCpmI1lJUrV9qCMjab361bN1vQa+JFnQu1lqngDcvqIKrcFTzpGMstt9xiV/xRxaHj7Xz44Yf2d1eRpGO/w3BOwjrWPR00b8cNN9xg5yto06aNXZUnlb0f/GXq8uXLbZCi86Rt8FPrpCbF1apl+tKhgCbI5azDcJ8guxBDZTZiqPQgfko9YqidQ/yUW/dJPCSpspwyzLoIXauHluJVBaNxu6JKXUGWbjStgpJsYVkKNhU0V4Ky/1o6VudEq6FoDolUFWhLliyxS9T6z7MqR42jbtmypZ1Lw2XUw1DxqYDVBIbnnHOO9+STT9rHtFxt1apVbQWiuRS0jLK/RTDd+53t5yTMY93TGZRu2LAh6vdUBwf6cvfyyy/bIRxatl5Dk9QCGHtu//rrL/tlMOjVgrL9PkH2IobKXMRQqUP8lB7EUEVH/JR790kiJKmyfJleTRypAk7eeOMNuxTvE088EVn9RYWhAh5dpKm4geiyumMqKFR4aFI9LWWdjG76mqjPvY+bWFHHV60NanWMLdQ1p0C/fv28bKdgSi1P/vtAlYeCWbVuaFJD1+KsgF6Brbqw+78ApErYzknYx7rvjHhDfArTiu8v+1JxbPzboBZvlb9umWS1fuu+uPjii73FixfH3abY90ilsN0nyE7EUNmDGCo5iJ/SixgqGvFT8X0XwvukICSpspguVI3RVUvHpEmTbHClcfyiwEqBVmzLX3EqdbqsFl2igqs4Ba1aeJs0aWLPrSZR1fF3rS4atqDxxgq6FWCLAvGjjjrKthhnM3Vp1b7qR8sjO66SV0CvSQ3d7zoGCgq0hHKqK/wwn5OwjnUvbouozqU7nwWVa+lcKvrhhx+28yQMHjw46rPffPNNG2ipnFarW1DCfJ8g+xBDZT5iqOQgfgoOMRTxUzLMCvl9Eg9JqiwRr7BSQaas+2GHHWaXuXRd+lyFpOBr1KhRSf/8XO6yms4CMxEV6JqTQUMEdtllF2/+/PlRz6sg1XwNWj5bE78ec8wxXtOmTbNi/HFBtPT2BRdcYFfWqFu3br4VZjQsQPvtKhFNujh06NDIOUtlhR/WcxLmse5F4b921MKsc61u1J07d873JTJRefH000/b7uPJoqEYDz74YOR39bbQdilw0ZAlURnttl2BVqNGjex9UdA2p1JY7xNkPmKozEAMFQzip2AQQxE/Jcs/Ib5PEiFJlWU3uLobavWZtWvXRrpBa8ysuvTpAtYYWRV4Crw0jjYZrW50Wf0f/75oiWrd+IVp8UhWUKb3cedTmXGNQd5///1tC7A7/o5agFVg6Tz8+9//DnRSv+LyHz8NjdBkhRMmTLAtG6ownIkTJ9qgfu+997bHRUMmkj2xaq6dkzCPdd8Zaq3S5Mpjx461QafOtcpflQex/NecjpOGTui6SAYd49dffz3SYuZo6IbmGNFnuaWb/Stj6b5RYJjucjns9wkyGzFUZiCGSj/ip2ARQ/0/4qedsz0H7pNESFJlEWVGtQKMVuLQjeS6iqpyUddRjR0/9NBDbWCl/0/2xZnLXVb9+6AgU5lq7aNWm3BBZrz99Be0Dz30UFRL7c5SK+yKFSu8pUuX2rkstB0vvvhi3GWBFXA72ZxN91NLgpbr1iSfav32B1oqtHV9umWr01U4h+Gc5NpY98LQ/evuYfVs0GSyWgJedP1Vq1bNGz58eMK/Ed3zVapUsRNxpoK+ePTo0SPyu+a1Udlcu3btSNmk6yy2fAqiXA7DfYLsRQwVHGKozED8lDrEUNGIn5JrXUjuk6IgSZXB/DeqJnHTfAWaO0ETGyqbqyDKBTfqPnrnnXfaLrvqFpnsZWLpshodZD7yyCO20jn55JNtt8u5c+fmK7j850/HRdnv8ePHF+vzdW4VZLiVJpRF1+8qrHRu3HlXMJ5pXeyLasyYMd6ZZ55pr3FN7CluSXBNYuiCqthAyy8dAVYYzkkujnUviIZFaKJi/3lasGCBLet0DcbOX6PjoeA+Ngh97LHHbICleWdSQdumljOdr2uvvTbyuIIr3RMa1qGgKxNa0sJwnyC7EENlHmKo9CB+Si9iqP9H/JR8T4fkPikqklQZKjZLq7G4anlzVPGcddZZdvypy0zHXowszZtc3377rde8eXPb5dMfeKolsGbNmlFzR8RrCfAHp8UpqFq1auWdd9553rx58+xjqvQ04aUKK10j6uapZYSz+fir1UVLbqvy0L4efvjh3rhx42wlpkBex1stCKIKT7+r9TkIYTgnuTjWPRENA9LEsmrl87feac4CtbBpUk0FWAqg/EGN5vqYM2dO1CScOpbJbAGMF3DoWlPvi7Jly0a1ymqb8vLy7D20fPlyL2hhuE+QPYihMg8xVHoQP6UfMdT/ED+lxtMhuU+KiiRVhlOWXa0euoF79eoV9dx7771nu6zrAp0yZUrSPpMuq/mD3N9//922RinInD59elSh98UXX9hj4Jax9hcQO9sSoPdN1J30hRdesMMRzj777Ehhpe6e3bt3t5NhakntbB+HrEBdLSsqcBXAagz7fvvtZyv4m2++2evdu7e9H3SctK8KuFRAp7ILbhjPSS6Pdd9RGXjllVfa688FSQoIdC4VtFx//fWR16oVUMdEAZi7PvTFSwGrlkxPFv+19/PPP9svG462TfdIbKClVjdtazrPURjvE2QvYqjgEEMFg/gpfYih8iN+2nlhvU92FkmqDOO/OG+99VbbLVrBlbqJlypVynZZ93v//fftCjSagDMZ6LKan/b78ssvtwWbVoS47rrrolo7RasD6XE/tQRovomdbQlwWX+dC7W2+qnwVkuMWoLVjdZdOwp+3d9lewuNri0F+LoG1XVYx18tqWoV1GONGzeOnAd/gZzqQCuM5yQXx7rH498flbX6AluhQgU7yaboXKongMrjq666ys6toi+eeswFB+591qxZk7L5XPSFTq2ymljZBVu6BxRoKUj2d1130h1ohfE+QeYjhso8xFDpR/yUXsRQxE/JEub7pKhIUmVwd93bb7/dtvSJCj6N81XApTkV/LRaTbIqFrqsRncJVSGhVU7cOGDtsyZWffLJJyOFqoIBdcN84IEHIn+nAkPLmD777LNF+my1cPmHJGiehj333NPr2bNnvmVPFXiohVFZdQXaifYhmymQ1zKxCqo0aaqjSs8V3qne17Cfk1wd616QG264wX6Z0nHQlyR1T3etelqJRtdE27ZtbTCqL6Lpmj9G84loQk+*********************************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",
      "text/plain": [
       "<Figure size 1200x600 with 2 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "# Análise de valores ausentes\n",
    "print(\"=== ANÁLISE DE VALORES AUSENTES ===\")\n",
    "\n",
    "# Contagem de valores nulos por coluna\n",
    "missing_data = df.isnull().sum()\n",
    "missing_percent = (missing_data / len(df)) * 100\n",
    "\n",
    "# Criar DataFrame com informações de missing values\n",
    "missing_df = pd.DataFrame({\n",
    "    'Coluna': missing_data.index,\n",
    "    'Valores_Ausentes': missing_data.values,\n",
    "    'Percentual': missing_percent.values\n",
    "})\n",
    "\n",
    "# Filtrar apenas colunas com valores ausentes\n",
    "missing_df = missing_df[missing_df['Valores_Ausentes'] > 0].sort_values('Valores_Ausentes', ascending=False)\n",
    "\n",
    "print(f\"Total de colunas com valores ausentes: {len(missing_df)}\")\n",
    "print(\"\\nColunas com valores ausentes:\")\n",
    "print(missing_df.to_string(index=False))\n",
    "\n",
    "# Visualização dos valores ausentes\n",
    "if len(missing_df) > 0:\n",
    "    plt.figure(figsize=(12, 6))\n",
    "    plt.subplot(1, 2, 1)\n",
    "    missing_df.head(10).plot(x='Coluna', y='Valores_Ausentes', kind='bar', ax=plt.gca())\n",
    "    plt.title('Top 10 Colunas com Valores Ausentes')\n",
    "    plt.xticks(rotation=45)\n",
    "    plt.tight_layout()\n",
    "    \n",
    "    plt.subplot(1, 2, 2)\n",
    "    missing_df.head(10).plot(x='Coluna', y='Percentual', kind='bar', ax=plt.gca())\n",
    "    plt.title('Percentual de Valores Ausentes')\n",
    "    plt.xticks(rotation=45)\n",
    "    plt.tight_layout()\n",
    "    \n",
    "    plt.show()\n",
    "else:\n",
    "    print(\"\\n✅ Não há valores ausentes no dataset!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 7,
   "id": "missing_values_treatment",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== ESTRATÉGIAS DE TRATAMENTO DE VALORES AUSENTES ===\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Segmentacao (6187 valores ausentes, 33.69%)\n",
      "  → Imputação com moda: 'CASUAL'\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Shape (6181 valores ausentes, 33.65%)\n",
      "  → Imputação com moda: 'QUADRADO'\n",
      "\n",
      "Tratando coluna: Dim_Cliente.Bairro_Cliente (344 valores ausentes, 1.87%)\n",
      "  → Imputação com moda: 'CENTRO'\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Cor2 (216 valores ausentes, 1.18%)\n",
      "  → Imputação com moda: 'PRETO                                   '\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Material2 (216 valores ausentes, 1.18%)\n",
      "  → Imputação com moda: 'POLICARBONATO                           '\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Sexo (216 valores ausentes, 1.18%)\n",
      "  → Imputação com moda: 'UNISSEX'\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Griffe (216 valores ausentes, 1.18%)\n",
      "  → Imputação com moda: 'CHILLI BEANS'\n",
      "\n",
      "Tratando coluna: Dim_Cliente.Cidade_cliente (101 valores ausentes, 0.55%)\n",
      "  → Imputação com moda: 'SÃO PAULO'\n",
      "\n",
      "Tratando coluna: Dim_Lojas.Bairro_Emp (14 valores ausentes, 0.08%)\n",
      "  → Imputação com moda: 'CENTRO'\n",
      "\n",
      "Tratando coluna: Dim_Produtos.Cor1 (10 valores ausentes, 0.05%)\n",
      "  → Imputação com moda: 'SORTIDO'\n",
      "\n",
      "=== RESULTADO DO TRATAMENTO ===\n",
      "Registros antes: 18,367\n",
      "Registros depois: 18,367\n",
      "Colunas antes: 66\n",
      "Colunas depois: 66\n",
      "Valores ausentes restantes: 0\n"
     ]
    }
   ],
   "source": [
    "# Estratégias de tratamento de valores ausentes\n",
    "print(\"=== ESTRATÉGIAS DE TRATAMENTO DE VALORES AUSENTES ===\")\n",
    "\n",
    "# Criar cópia do dataframe para tratamento\n",
    "df_treated = df.copy()\n",
    "acoes_realizadas = []\n",
    "\n",
    "# Verificar se há valores ausentes para tratar\n",
    "if len(missing_df) > 0:\n",
    "    for _, row in missing_df.iterrows():\n",
    "        coluna = row['Coluna']\n",
    "        valores_ausentes = row['Valores_Ausentes']\n",
    "        percentual = row['Percentual']\n",
    "        \n",
    "        print(f\"\\nTratando coluna: {coluna} ({valores_ausentes} valores ausentes, {percentual:.2f}%)\")\n",
    "        \n",
    "        # Estratégia baseada no tipo de dados e percentual de missing\n",
    "        if percentual > 50:\n",
    "            # Remover colunas com mais de 50% de valores ausentes\n",
    "            df_treated = df_treated.drop(columns=[coluna])\n",
    "            acao = f\"Remoção da coluna (>50% ausentes)\"\n",
    "            print(f\"  → {acao}\")\n",
    "            acoes_realizadas.append(f\"{coluna}: {acao}\")\n",
    "            \n",
    "        elif df_treated[coluna].dtype in ['object']:\n",
    "            # Para categóricas: imputar com moda\n",
    "            moda = df_treated[coluna].mode()[0] if not df_treated[coluna].mode().empty else 'Não informado'\n",
    "            df_treated[coluna].fillna(moda, inplace=True)\n",
    "            acao = f\"Imputação com moda: '{moda}'\"\n",
    "            print(f\"  → {acao}\")\n",
    "            acoes_realizadas.append(f\"{coluna}: {acao}\")\n",
    "            \n",
    "        elif df_treated[coluna].dtype in ['int64', 'float64']:\n",
    "            # Para numéricas: imputar com mediana (mais robusta a outliers)\n",
    "            mediana = df_treated[coluna].median()\n",
    "            df_treated[coluna].fillna(mediana, inplace=True)\n",
    "            acao = f\"Imputação com mediana: {mediana}\"\n",
    "            print(f\"  → {acao}\")\n",
    "            acoes_realizadas.append(f\"{coluna}: {acao}\")\n",
    "            \n",
    "        else:\n",
    "            # Para outros tipos: remover registros\n",
    "            df_treated = df_treated.dropna(subset=[coluna])\n",
    "            acao = f\"Remoção de registros com valores ausentes\"\n",
    "            print(f\"  → {acao}\")\n",
    "            acoes_realizadas.append(f\"{coluna}: {acao}\")\n",
    "\n",
    "    print(f\"\\n=== RESULTADO DO TRATAMENTO ===\")\n",
    "    print(f\"Registros antes: {len(df):,}\")\n",
    "    print(f\"Registros depois: {len(df_treated):,}\")\n",
    "    print(f\"Colunas antes: {df.shape[1]}\")\n",
    "    print(f\"Colunas depois: {df_treated.shape[1]}\")\n",
    "    \n",
    "    # Verificar se ainda há valores ausentes\n",
    "    remaining_missing = df_treated.isnull().sum().sum()\n",
    "    print(f\"Valores ausentes restantes: {remaining_missing}\")\n",
    "    \n",
    "else:\n",
    "    print(\"✅ Não há valores ausentes para tratar!\")\n",
    "    acoes_realizadas.append(\"Nenhuma ação necessária - dataset sem valores ausentes\")\n",
    "\n",
    "# Atualizar dataframe principal\n",
    "df = df_treated.copy()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step3_header",
   "metadata": {},
   "source": [
    "## 3. Detecção e Tratamento de Outliers\n",
    "\n",
    "Identificação de outliers em colunas numéricas relevantes usando técnicas estatísticas."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 8,
   "id": "outlier_detection",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== DETECÇÃO DE OUTLIERS ===\n",
      "\n",
      "--- Análise de outliers: Quantidade ---\n",
      "Estatísticas básicas:\n",
      "  Média: 1.09\n",
      "  Mediana: 1.00\n",
      "  Desvio padrão: 0.33\n",
      "  Min: 1.00\n",
      "  Max: 12.00\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [1.00, 1.00]\n",
      "  Outliers encontrados: 1536 (8.36%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 78 (0.42%)\n",
      "\n",
      "--- Análise de outliers: Preco_Custo ---\n",
      "Estatísticas básicas:\n",
      "  Média: 76.37\n",
      "  Mediana: 73.26\n",
      "  Desvio padrão: 75.19\n",
      "  Min: 1.80\n",
      "  Max: 1379.16\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [-120.02, 232.00]\n",
      "  Outliers encontrados: 402 (2.19%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 258 (1.40%)\n",
      "\n",
      "--- Análise de outliers: Valor_Total ---\n",
      "Estatísticas básicas:\n",
      "  Média: 305.63\n",
      "  Mediana: 322.14\n",
      "  Desvio padrão: 316.62\n",
      "  Min: 0.01\n",
      "  Max: 11057.97\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [-479.97, 927.95]\n",
      "  Outliers encontrados: 383 (2.09%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 239 (1.30%)\n",
      "\n",
      "--- Análise de outliers: Preco_Varejo ---\n",
      "Estatísticas básicas:\n",
      "  Média: 285.49\n",
      "  Mediana: 329.50\n",
      "  Desvio padrão: 222.19\n",
      "  Min: 3.31\n",
      "  Max: 5635.50\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [-525.02, 954.98]\n",
      "  Outliers encontrados: 174 (0.95%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 175 (0.95%)\n",
      "\n",
      "--- Análise de outliers: Frete ---\n",
      "Estatísticas básicas:\n",
      "  Média: 0.00\n",
      "  Mediana: 0.00\n",
      "  Desvio padrão: 0.07\n",
      "  Min: 0.00\n",
      "  Max: 10.00\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [0.00, 0.00]\n",
      "  Outliers encontrados: 1 (0.01%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 1 (0.01%)\n",
      "\n",
      "--- Análise de outliers: Desconto ---\n",
      "Estatísticas básicas:\n",
      "  Média: 22.04\n",
      "  Mediana: 0.00\n",
      "  Desvio padrão: 135.30\n",
      "  Min: 0.00\n",
      "  Max: 4651.00\n",
      "\n",
      "Outliers por IQR:\n",
      "  Limites: [0.00, 0.00]\n",
      "  Outliers encontrados: 2646 (14.41%)\n",
      "\n",
      "Outliers por Z-score (threshold=3):\n",
      "  Outliers encontrados: 111 (0.60%)\n",
      "\n",
      "=== RESUMO DE OUTLIERS ===\n",
      "      coluna  outliers_iqr  outliers_zscore  lower_iqr  upper_iqr  percentual_iqr\n",
      "  Quantidade          1536               78     1.0000     1.0000        8.362825\n",
      " Preco_Custo           402              258  -120.0175   232.0025        2.188708\n",
      " Valor_Total           383              239  -479.9700   927.9500        2.085262\n",
      "Preco_Varejo           174              175  -525.0200   954.9800        0.947351\n",
      "       Frete             1                1     0.0000     0.0000        0.005445\n",
      "    Desconto          2646              111     0.0000     0.0000       14.406272\n"
     ]
    }
   ],
   "source": [
    "# Função para detectar outliers usando IQR\n",
    "def detect_outliers_iqr(data, column):\n",
    "    Q1 = data[column].quantile(0.25)\n",
    "    Q3 = data[column].quantile(0.75)\n",
    "    IQR = Q3 - Q1\n",
    "    lower_bound = Q1 - 1.5 * IQR\n",
    "    upper_bound = Q3 + 1.5 * IQR\n",
    "    \n",
    "    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]\n",
    "    return outliers, lower_bound, upper_bound\n",
    "\n",
    "# Função para detectar outliers usando Z-score\n",
    "def detect_outliers_zscore(data, column, threshold=3):\n",
    "    z_scores = np.abs(stats.zscore(data[column]))\n",
    "    outliers = data[z_scores > threshold]\n",
    "    return outliers, threshold\n",
    "\n",
    "print(\"=== DETECÇÃO DE OUTLIERS ===\")\n",
    "\n",
    "# Colunas numéricas para análise de outliers\n",
    "colunas_outliers = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']\n",
    "colunas_outliers = [col for col in colunas_outliers if col in df.columns]\n",
    "\n",
    "outliers_info = []\n",
    "\n",
    "for coluna in colunas_outliers:\n",
    "    print(f\"\\n--- Análise de outliers: {coluna} ---\")\n",
    "    \n",
    "    # Estatísticas básicas\n",
    "    print(f\"Estatísticas básicas:\")\n",
    "    print(f\"  Média: {df[coluna].mean():.2f}\")\n",
    "    print(f\"  Mediana: {df[coluna].median():.2f}\")\n",
    "    print(f\"  Desvio padrão: {df[coluna].std():.2f}\")\n",
    "    print(f\"  Min: {df[coluna].min():.2f}\")\n",
    "    print(f\"  Max: {df[coluna].max():.2f}\")\n",
    "    \n",
    "    # Detecção por IQR\n",
    "    outliers_iqr, lower_iqr, upper_iqr = detect_outliers_iqr(df, coluna)\n",
    "    print(f\"\\nOutliers por IQR:\")\n",
    "    print(f\"  Limites: [{lower_iqr:.2f}, {upper_iqr:.2f}]\")\n",
    "    print(f\"  Outliers encontrados: {len(outliers_iqr)} ({len(outliers_iqr)/len(df)*100:.2f}%)\")\n",
    "    \n",
    "    # Detecção por Z-score\n",
    "    outliers_zscore, threshold = detect_outliers_zscore(df, coluna)\n",
    "    print(f\"\\nOutliers por Z-score (threshold={threshold}):\")\n",
    "    print(f\"  Outliers encontrados: {len(outliers_zscore)} ({len(outliers_zscore)/len(df)*100:.2f}%)\")\n",
    "    \n",
    "    # Armazenar informações\n",
    "    outliers_info.append({\n",
    "        'coluna': coluna,\n",
    "        'outliers_iqr': len(outliers_iqr),\n",
    "        'outliers_zscore': len(outliers_zscore),\n",
    "        'lower_iqr': lower_iqr,\n",
    "        'upper_iqr': upper_iqr,\n",
    "        'percentual_iqr': len(outliers_iqr)/len(df)*100\n",
    "    })\n",
    "\n",
    "# Resumo dos outliers\n",
    "outliers_summary = pd.DataFrame(outliers_info)\n",
    "print(\"\\n=== RESUMO DE OUTLIERS ===\")\n",
    "print(outliers_summary.to_string(index=False))"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "id": "outlier_visualization",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== VISUALIZAÇÃO DE OUTLIERS ===\n"
     ]
    },
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1800x1200 with 6 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 1800x1200 with 6 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "# Visualização de outliers\n",
    "print(\"=== VISUALIZAÇÃO DE OUTLIERS ===\")\n",
    "\n",
    "# Criar subplots para boxplots\n",
    "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "axes = axes.ravel()\n",
    "\n",
    "for i, coluna in enumerate(colunas_outliers):\n",
    "    if i < len(axes):\n",
    "        # Boxplot\n",
    "        df.boxplot(column=coluna, ax=axes[i])\n",
    "        axes[i].set_title(f'Boxplot - {coluna}')\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "\n",
    "# Remover subplots vazios\n",
    "for i in range(len(colunas_outliers), len(axes)):\n",
    "    fig.delaxes(axes[i])\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Histogramas para visualizar distribuições\n",
    "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "axes = axes.ravel()\n",
    "\n",
    "for i, coluna in enumerate(colunas_outliers):\n",
    "    if i < len(axes):\n",
    "        axes[i].hist(df[coluna], bins=50, alpha=0.7, edgecolor='black')\n",
    "        axes[i].set_title(f'Distribuição - {coluna}')\n",
    "        axes[i].set_xlabel(coluna)\n",
    "        axes[i].set_ylabel('Frequência')\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "\n",
    "# Remover subplots vazios\n",
    "for i in range(len(colunas_outliers), len(axes)):\n",
    "    fig.delaxes(axes[i])\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 10,
   "id": "outlier_treatment",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== TRATAMENTO DE OUTLIERS ===\n",
      "\n",
      "--- Tratamento de outliers: Quantidade ---\n",
      "Outliers detectados: 1536 (8.36%)\n",
      "  → Substituindo por percentis 5% e 95%\n",
      "\n",
      "--- Tratamento de outliers: Preco_Custo ---\n",
      "Outliers detectados: 402 (2.19%)\n",
      "  → Removendo outliers extremos\n",
      "\n",
      "--- Tratamento de outliers: Valor_Total ---\n",
      "Outliers detectados: 383 (2.09%)\n",
      "  → Removendo outliers extremos\n",
      "\n",
      "--- Tratamento de outliers: Preco_Varejo ---\n",
      "Outliers detectados: 174 (0.95%)\n",
      "  → Mantendo outliers (<1% dos dados)\n",
      "\n",
      "--- Tratamento de outliers: Frete ---\n",
      "Outliers detectados: 1 (0.01%)\n",
      "  → Mantendo outliers (<1% dos dados)\n",
      "\n",
      "--- Tratamento de outliers: Desconto ---\n",
      "Outliers detectados: 2646 (14.41%)\n",
      "  → Aplicando cap truncation (>10% outliers)\n",
      "\n",
      "=== RESULTADO DO TRATAMENTO DE OUTLIERS ===\n",
      "Registros antes: 18,367\n",
      "Registros depois: 17,868\n",
      "Registros removidos: 499 (2.72%)\n"
     ]
    }
   ],
   "source": [
    "# Tratamento de outliers\n",
    "print(\"=== TRATAMENTO DE OUTLIERS ===\")\n",
    "\n",
    "df_outlier_treated = df.copy()\n",
    "acoes_outliers = []\n",
    "\n",
    "for info in outliers_info:\n",
    "    coluna = info['coluna']\n",
    "    percentual_iqr = info['percentual_iqr']\n",
    "    lower_iqr = info['lower_iqr']\n",
    "    upper_iqr = info['upper_iqr']\n",
    "    \n",
    "    print(f\"\\n--- Tratamento de outliers: {coluna} ---\")\n",
    "    print(f\"Outliers detectados: {info['outliers_iqr']} ({percentual_iqr:.2f}%)\")\n",
    "    \n",
    "    # Estratégia baseada no percentual de outliers e contexto de negócio\n",
    "    if percentual_iqr > 10:\n",
    "        # Se mais de 10% são outliers, usar cap truncation (winsorização)\n",
    "        print(f\"  → Aplicando cap truncation (>10% outliers)\")\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] < lower_iqr, lower_iqr, df_outlier_treated[coluna]\n",
    "        )\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] > upper_iqr, upper_iqr, df_outlier_treated[coluna]\n",
    "        )\n",
    "        acao = f\"Cap truncation nos limites [{lower_iqr:.2f}, {upper_iqr:.2f}]\"\n",
    "        \n",
    "    elif percentual_iqr > 5:\n",
    "        # Entre 5-10%, substituir por percentis 5 e 95\n",
    "        print(f\"  → Substituindo por percentis 5% e 95%\")\n",
    "        p5 = df_outlier_treated[coluna].quantile(0.05)\n",
    "        p95 = df_outlier_treated[coluna].quantile(0.95)\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] < p5, p5, df_outlier_treated[coluna]\n",
    "        )\n",
    "        df_outlier_treated[coluna] = np.where(\n",
    "            df_outlier_treated[coluna] > p95, p95, df_outlier_treated[coluna]\n",
    "        )\n",
    "        acao = f\"Substituição por percentis 5% ({p5:.2f}) e 95% ({p95:.2f})\"\n",
    "        \n",
    "    elif percentual_iqr > 1:\n",
    "        # Entre 1-5%, remover outliers extremos\n",
    "        print(f\"  → Removendo outliers extremos\")\n",
    "        outliers_mask = (df_outlier_treated[coluna] < lower_iqr) | (df_outlier_treated[coluna] > upper_iqr)\n",
    "        df_outlier_treated = df_outlier_treated[~outliers_mask]\n",
    "        acao = f\"Remoção de {outliers_mask.sum()} registros outliers\"\n",
    "        \n",
    "    else:\n",
    "        # Menos de 1%, manter como está\n",
    "        print(f\"  → Mantendo outliers (<1% dos dados)\")\n",
    "        acao = \"Nenhuma ação - outliers mantidos (<1%)\"\n",
    "    \n",
    "    acoes_outliers.append(f\"{coluna}: {acao}\")\n",
    "\n",
    "print(f\"\\n=== RESULTADO DO TRATAMENTO DE OUTLIERS ===\")\n",
    "print(f\"Registros antes: {len(df):,}\")\n",
    "print(f\"Registros depois: {len(df_outlier_treated):,}\")\n",
    "registros_removidos = len(df) - len(df_outlier_treated)\n",
    "print(f\"Registros removidos: {registros_removidos:,} ({registros_removidos/len(df)*100:.2f}%)\")\n",
    "\n",
    "# Atualizar dataframe principal\n",
    "df = df_outlier_treated.copy()"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step4_header",
   "metadata": {},
   "source": [
    "## 4. Normalização/Padronização de Colunas Numéricas\n",
    "\n",
    "Aplicação de transformações de escala nas colunas numéricas."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 11,
   "id": "normalization",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== NORMALIZAÇÃO/PADRONIZAÇÃO ===\n",
      "\n",
      "--- Aplicando Normalização Min-Max (0-1) ---\n",
      "Normalizando: Quantidade\n",
      "  Original: [1.00, 2.00] → Normalizado: [0.00, 1.00]\n",
      "Normalizando: Preco_Custo\n",
      "  Original: [1.80, 231.47] → Normalizado: [0.00, 1.00]\n",
      "Normalizando: Valor_Total\n",
      "  Original: [0.01, 923.00] → Normalizado: [0.00, 1.00]\n",
      "Normalizando: Preco_Varejo\n",
      "  Original: [3.31, 899.98] → Normalizado: [0.00, 1.00]\n",
      "Normalizando: Frete\n",
      "  Original: [0.00, 10.00] → Normalizado: [0.00, 1.00]\n",
      "Normalizando: Desconto\n",
      "  Original: [0.00, 0.00] → Normalizado: [0.00, 0.00]\n",
      "\n",
      "--- Aplicando Padronização Z-score (μ=0, σ=1) ---\n",
      "Padronizando: Quantidade\n",
      "  Original: μ=1.07, σ=0.25 → Padronizado: μ=0.00, σ=1.00\n",
      "Padronizando: Preco_Custo\n",
      "  Original: μ=68.35, σ=48.97 → Padronizado: μ=0.00, σ=1.00\n",
      "Padronizando: Valor_Total\n",
      "  Original: μ=271.89, σ=182.90 → Padronizado: μ=0.00, σ=1.00\n",
      "Padronizando: Preco_Varejo\n",
      "  Original: μ=268.07, σ=179.80 → Padronizado: μ=0.00, σ=1.00\n",
      "Padronizando: Frete\n",
      "  Original: μ=0.00, σ=0.07 → Padronizado: μ=-0.00, σ=1.00\n",
      "Padronizando: Desconto\n",
      "  Original: μ=0.00, σ=0.00 → Padronizado: μ=0.00, σ=0.00\n",
      "\n",
      "✅ Transformações de escala aplicadas em 6 colunas\n"
     ]
    }
   ],
   "source": [
    "print(\"=== NORMALIZAÇÃO/PADRONIZAÇÃO ===\")\n",
    "\n",
    "# Colunas para normalização (excluindo IDs e colunas calculadas)\n",
    "colunas_para_normalizar = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']\n",
    "colunas_para_normalizar = [col for col in colunas_para_normalizar if col in df.columns]\n",
    "\n",
    "# Criar cópia para transformações\n",
    "df_normalized = df.copy()\n",
    "acoes_normalizacao = []\n",
    "\n",
    "# Aplicar Min-Max Normalization (0-1)\n",
    "print(\"\\n--- Aplicando Normalização Min-Max (0-1) ---\")\n",
    "scaler_minmax = MinMaxScaler()\n",
    "\n",
    "for coluna in colunas_para_normalizar:\n",
    "    print(f\"Normalizando: {coluna}\")\n",
    "    \n",
    "    # Valores originais\n",
    "    original_min = df[coluna].min()\n",
    "    original_max = df[coluna].max()\n",
    "    \n",
    "    # Aplicar normalização\n",
    "    df_normalized[f'{coluna}_normalized'] = scaler_minmax.fit_transform(df[[coluna]])\n",
    "    \n",
    "    # Verificar resultado\n",
    "    new_min = df_normalized[f'{coluna}_normalized'].min()\n",
    "    new_max = df_normalized[f'{coluna}_normalized'].max()\n",
    "    \n",
    "    print(f\"  Original: [{original_min:.2f}, {original_max:.2f}] → Normalizado: [{new_min:.2f}, {new_max:.2f}]\")\n",
    "    acoes_normalizacao.append(f\"{coluna} → normalização Min-Max (0-1)\")\n",
    "\n",
    "# Aplicar Padronização Z-score (média=0, desvio=1)\n",
    "print(\"\\n--- Aplicando Padronização Z-score (μ=0, σ=1) ---\")\n",
    "scaler_standard = StandardScaler()\n",
    "\n",
    "for coluna in colunas_para_normalizar:\n",
    "    print(f\"Padronizando: {coluna}\")\n",
    "    \n",
    "    # Valores originais\n",
    "    original_mean = df[coluna].mean()\n",
    "    original_std = df[coluna].std()\n",
    "    \n",
    "    # Aplicar padronização\n",
    "    df_normalized[f'{coluna}_standardized'] = scaler_standard.fit_transform(df[[coluna]])\n",
    "    \n",
    "    # Verificar resultado\n",
    "    new_mean = df_normalized[f'{coluna}_standardized'].mean()\n",
    "    new_std = df_normalized[f'{coluna}_standardized'].std()\n",
    "    \n",
    "    print(f\"  Original: μ={original_mean:.2f}, σ={original_std:.2f} → Padronizado: μ={new_mean:.2f}, σ={new_std:.2f}\")\n",
    "    acoes_normalizacao.append(f\"{coluna} → padronização Z-score (μ=0, σ=1)\")\n",
    "\n",
    "print(f\"\\n✅ Transformações de escala aplicadas em {len(colunas_para_normalizar)} colunas\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step5_header",
   "metadata": {},
   "source": [
    "## 5. Codificação de Variáveis Categóricas\n",
    "\n",
    "Aplicação de One-Hot Encoding para variáveis nominais e Label Encoding para ordinais."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 12,
   "id": "categorical_encoding",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS ===\n",
      "\n",
      "Variáveis nominais para One-Hot Encoding: 7\n",
      "Variáveis ordinais para Label Encoding: 2\n",
      "\n",
      "--- One-Hot Encoding (Variáveis Nominais) ---\n",
      "\n",
      "Processando: Dim_Lojas.Tipo_PDV\n",
      "  Categorias únicas: 15\n",
      "  → Criadas 14 colunas dummy\n",
      "\n",
      "Processando: Dim_Lojas.CANAL_VENDA\n",
      "  Categorias únicas: 3\n",
      "  → Criadas 2 colunas dummy\n",
      "\n",
      "Processando: Dim_Lojas.REGIAO_CHILLI\n",
      "  Categorias únicas: 5\n",
      "  → Criadas 4 colunas dummy\n",
      "\n",
      "Processando: Dim_Cliente.Uf_Cliente\n",
      "  Categorias únicas: 47\n",
      "  → Muitas categorias (47), mantendo top 10\n",
      "  → Categorias após agrupamento: 11\n",
      "  → Criadas 10 colunas dummy\n",
      "\n",
      "Processando: Dim_Produtos.Grupo_Produto\n",
      "  Categorias únicas: 12\n",
      "  → Criadas 11 colunas dummy\n",
      "\n",
      "Processando: Dim_Produtos.Sub_Grupo\n",
      "  Categorias únicas: 38\n",
      "  → Muitas categorias (38), mantendo top 10\n",
      "  → Categorias após agrupamento: 11\n",
      "  → Criadas 10 colunas dummy\n",
      "\n",
      "Processando: Dim_Produtos.Sexo\n",
      "  Categorias únicas: 7\n",
      "  → Criadas 6 colunas dummy\n",
      "\n",
      "--- Label Encoding (Variáveis Ordinais) ---\n",
      "\n",
      "Processando: Dim_Cliente.Sexo\n",
      "  Valores únicos: ['M' ' ' 'S' 'F']\n",
      "  → Mapeamento: {' ': np.int64(0), 'F': np.int64(1), 'M': np.int64(2), 'S': np.int64(3)}\n",
      "\n",
      "Processando: Dim_Cliente.Estado_Civil\n",
      "  Valores únicos: ['Outros' 'Casado' 'Não informado' 'Solteiro' 'Divorciado' 'Viúvo']\n",
      "  → Mapeamento: {'Casado': np.int64(0), 'Divorciado': np.int64(1), 'Não informado': np.int64(2), 'Outros': np.int64(3), 'Solteiro': np.int64(4), 'Viúvo': np.int64(5)}\n",
      "\n",
      "=== RESULTADO DA CODIFICAÇÃO ===\n",
      "Colunas antes: 78\n",
      "Colunas depois: 137\n",
      "Novas colunas criadas: 59\n"
     ]
    }
   ],
   "source": [
    "print(\"=== CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS ===\")\n",
    "\n",
    "# Criar cópia para codificação\n",
    "df_encoded = df_normalized.copy()\n",
    "acoes_codificacao = []\n",
    "\n",
    "# Definir variáveis categóricas nominais (One-Hot Encoding)\n",
    "variaveis_nominais = [\n",
    "    'Dim_Lojas.Tipo_PDV',\n",
    "    'Dim_Lojas.CANAL_VENDA', \n",
    "    'Dim_Lojas.REGIAO_CHILLI',\n",
    "    'Dim_Cliente.Uf_Cliente',\n",
    "    'Dim_Produtos.Grupo_Produto',\n",
    "    'Dim_Produtos.Sub_Grupo',\n",
    "    'Dim_Produtos.Sexo'\n",
    "]\n",
    "\n",
    "# Definir variáveis categóricas ordinais (Label Encoding)\n",
    "variaveis_ordinais = [\n",
    "    'Dim_Cliente.Sexo',  # M, F\n",
    "    'Dim_Cliente.Estado_Civil'  # Solteiro, Casado, etc.\n",
    "]\n",
    "\n",
    "# Filtrar apenas colunas existentes\n",
    "variaveis_nominais = [col for col in variaveis_nominais if col in df_encoded.columns]\n",
    "variaveis_ordinais = [col for col in variaveis_ordinais if col in df_encoded.columns]\n",
    "\n",
    "print(f\"\\nVariáveis nominais para One-Hot Encoding: {len(variaveis_nominais)}\")\n",
    "print(f\"Variáveis ordinais para Label Encoding: {len(variaveis_ordinais)}\")\n",
    "\n",
    "# Aplicar One-Hot Encoding para variáveis nominais\n",
    "print(\"\\n--- One-Hot Encoding (Variáveis Nominais) ---\")\n",
    "for coluna in variaveis_nominais:\n",
    "    print(f\"\\nProcessando: {coluna}\")\n",
    "    \n",
    "    # Verificar número de categorias únicas\n",
    "    categorias_unicas = df_encoded[coluna].nunique()\n",
    "    print(f\"  Categorias únicas: {categorias_unicas}\")\n",
    "    \n",
    "    if categorias_unicas > 20:\n",
    "        # Para muitas categorias, manter apenas as top 10 mais frequentes\n",
    "        print(f\"  → Muitas categorias ({categorias_unicas}), mantendo top 10\")\n",
    "        top_categories = df_encoded[coluna].value_counts().head(10).index.tolist()\n",
    "        df_encoded[coluna] = df_encoded[coluna].apply(\n",
    "            lambda x: x if x in top_categories else 'Outros'\n",
    "        )\n",
    "        categorias_unicas = df_encoded[coluna].nunique()\n",
    "        print(f\"  → Categorias após agrupamento: {categorias_unicas}\")\n",
    "    \n",
    "    # Aplicar One-Hot Encoding\n",
    "    dummies = pd.get_dummies(df_encoded[coluna], prefix=coluna, drop_first=True)\n",
    "    df_encoded = pd.concat([df_encoded, dummies], axis=1)\n",
    "    \n",
    "    print(f\"  → Criadas {len(dummies.columns)} colunas dummy\")\n",
    "    acoes_codificacao.append(f\"{coluna} → One-Hot Encoding ({len(dummies.columns)} colunas criadas)\")\n",
    "\n",
    "# Aplicar Label Encoding para variáveis ordinais\n",
    "print(\"\\n--- Label Encoding (Variáveis Ordinais) ---\")\n",
    "label_encoders = {}\n",
    "\n",
    "for coluna in variaveis_ordinais:\n",
    "    print(f\"\\nProcessando: {coluna}\")\n",
    "    \n",
    "    # Verificar valores únicos\n",
    "    valores_unicos = df_encoded[coluna].unique()\n",
    "    print(f\"  Valores únicos: {valores_unicos}\")\n",
    "    \n",
    "    # Aplicar Label Encoding\n",
    "    le = LabelEncoder()\n",
    "    df_encoded[f'{coluna}_encoded'] = le.fit_transform(df_encoded[coluna].astype(str))\n",
    "    \n",
    "    # Armazenar encoder para referência\n",
    "    label_encoders[coluna] = le\n",
    "    \n",
    "    # Mostrar mapeamento\n",
    "    mapping = dict(zip(le.classes_, le.transform(le.classes_)))\n",
    "    print(f\"  → Mapeamento: {mapping}\")\n",
    "    acoes_codificacao.append(f\"{coluna} → Label Encoding (mapeamento: {mapping})\")\n",
    "\n",
    "print(f\"\\n=== RESULTADO DA CODIFICAÇÃO ===\")\n",
    "print(f\"Colunas antes: {df_normalized.shape[1]}\")\n",
    "print(f\"Colunas depois: {df_encoded.shape[1]}\")\n",
    "novas_colunas = df_encoded.shape[1] - df_normalized.shape[1]\n",
    "print(f\"Novas colunas criadas: {novas_colunas}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "step6_header",
   "metadata": {},
   "source": [
    "## 6. Documentação e Validação Final\n",
    "\n",
    "Documentação completa de todas as transformações aplicadas e validação do dataset final."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 13,
   "id": "final_documentation",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "=== DOCUMENTAÇÃO COMPLETA DAS TRANSFORMAÇÕES ===\n",
      "\n",
      "1. TRATAMENTO DE VALORES AUSENTES:\n",
      "   • Dim_Produtos.Segmentacao: Imputação com moda: 'CASUAL'\n",
      "   • Dim_Produtos.Shape: Imputação com moda: 'QUADRADO'\n",
      "   • Dim_Cliente.Bairro_Cliente: Imputação com moda: 'CENTRO'\n",
      "   • Dim_Produtos.Cor2: Imputação com moda: 'PRETO                                   '\n",
      "   • Dim_Produtos.Material2: Imputação com moda: 'POLICARBONATO                           '\n",
      "   • Dim_Produtos.Sexo: Imputação com moda: 'UNISSEX'\n",
      "   • Dim_Produtos.Griffe: Imputação com moda: 'CHILLI BEANS'\n",
      "   • Dim_Cliente.Cidade_cliente: Imputação com moda: 'SÃO PAULO'\n",
      "   • Dim_Lojas.Bairro_Emp: Imputação com moda: 'CENTRO'\n",
      "   • Dim_Produtos.Cor1: Imputação com moda: 'SORTIDO'\n",
      "\n",
      "2. TRATAMENTO DE OUTLIERS:\n",
      "   • Quantidade: Substituição por percentis 5% (1.00) e 95% (2.00)\n",
      "   • Preco_Custo: Remoção de 402 registros outliers\n",
      "   • Valor_Total: Remoção de 97 registros outliers\n",
      "   • Preco_Varejo: Nenhuma ação - outliers mantidos (<1%)\n",
      "   • Frete: Nenhuma ação - outliers mantidos (<1%)\n",
      "   • Desconto: Cap truncation nos limites [0.00, 0.00]\n",
      "\n",
      "3. NORMALIZAÇÃO/PADRONIZAÇÃO:\n",
      "   • Quantidade → normalização Min-Max (0-1)\n",
      "   • Preco_Custo → normalização Min-Max (0-1)\n",
      "   • Valor_Total → normalização Min-Max (0-1)\n",
      "   • Preco_Varejo → normalização Min-Max (0-1)\n",
      "   • Frete → normalização Min-Max (0-1)\n",
      "   • Desconto → normalização Min-Max (0-1)\n",
      "   • Quantidade → padronização Z-score (μ=0, σ=1)\n",
      "   • Preco_Custo → padronização Z-score (μ=0, σ=1)\n",
      "   • Valor_Total → padronização Z-score (μ=0, σ=1)\n",
      "   • Preco_Varejo → padronização Z-score (μ=0, σ=1)\n",
      "   • Frete → padronização Z-score (μ=0, σ=1)\n",
      "   • Desconto → padronização Z-score (μ=0, σ=1)\n",
      "\n",
      "4. CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS:\n",
      "   • Dim_Lojas.Tipo_PDV → One-Hot Encoding (14 colunas criadas)\n",
      "   • Dim_Lojas.CANAL_VENDA → One-Hot Encoding (2 colunas criadas)\n",
      "   • Dim_Lojas.REGIAO_CHILLI → One-Hot Encoding (4 colunas criadas)\n",
      "   • Dim_Cliente.Uf_Cliente → One-Hot Encoding (10 colunas criadas)\n",
      "   • Dim_Produtos.Grupo_Produto → One-Hot Encoding (11 colunas criadas)\n",
      "   • Dim_Produtos.Sub_Grupo → One-Hot Encoding (10 colunas criadas)\n",
      "   • Dim_Produtos.Sexo → One-Hot Encoding (6 colunas criadas)\n",
      "   • Dim_Cliente.Sexo → Label Encoding (mapeamento: {' ': np.int64(0), 'F': np.int64(1), 'M': np.int64(2), 'S': np.int64(3)})\n",
      "   • Dim_Cliente.Estado_Civil → Label Encoding (mapeamento: {'Casado': np.int64(0), 'Divorciado': np.int64(1), 'Não informado': np.int64(2), 'Outros': np.int64(3), 'Solteiro': np.int64(4), 'Viúvo': np.int64(5)})\n",
      "\n",
      "=== RESUMO FINAL DO DATASET ===\n",
      "Dataset original: 40,291 registros, 61 colunas\n",
      "Dataset filtrado: 17,868 registros, 66 colunas\n",
      "Dataset final processado: 17,868 registros, 137 colunas\n",
      "\n",
      "=== VERIFICAÇÕES FINAIS ===\n",
      "✅ Valores ausentes restantes: 0\n",
      "✅ Tipos de dados:\n",
      "   • bool: 57 colunas\n",
      "   • object: 40 colunas\n",
      "   • float64: 27 colunas\n",
      "   • int64: 10 colunas\n",
      "   • datetime64[ns]: 3 colunas\n",
      "\n",
      "✅ Dataset processado salvo em: ../assets/dados_processados.csv\n",
      "\n",
      "🎯 PRÉ-PROCESSAMENTO CONCLUÍDO COM SUCESSO!\n",
      "   O dataset está pronto para uso em modelos preditivos.\n"
     ]
    }
   ],
   "source": [
    "print(\"=== DOCUMENTAÇÃO COMPLETA DAS TRANSFORMAÇÕES ===\")\n",
    "\n",
    "print(\"\\n1. TRATAMENTO DE VALORES AUSENTES:\")\n",
    "for acao in acoes_realizadas:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n2. TRATAMENTO DE OUTLIERS:\")\n",
    "for acao in acoes_outliers:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n3. NORMALIZAÇÃO/PADRONIZAÇÃO:\")\n",
    "for acao in acoes_normalizacao:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n4. CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS:\")\n",
    "for acao in acoes_codificacao:\n",
    "    print(f\"   • {acao}\")\n",
    "\n",
    "print(\"\\n=== RESUMO FINAL DO DATASET ===\")\n",
    "print(f\"Dataset original: {40291:,} registros, {61} colunas\")\n",
    "print(f\"Dataset filtrado: {len(df):,} registros, {df.shape[1]} colunas\")\n",
    "print(f\"Dataset final processado: {len(df_encoded):,} registros, {df_encoded.shape[1]} colunas\")\n",
    "\n",
    "# Verificações finais\n",
    "print(\"\\n=== VERIFICAÇÕES FINAIS ===\")\n",
    "print(f\"✅ Valores ausentes restantes: {df_encoded.isnull().sum().sum()}\")\n",
    "print(f\"✅ Tipos de dados:\")\n",
    "tipos_finais = df_encoded.dtypes.value_counts()\n",
    "for tipo, count in tipos_finais.items():\n",
    "    print(f\"   • {tipo}: {count} colunas\")\n",
    "\n",
    "# Salvar dataset processado\n",
    "output_path = '../assets/dados_processados.csv'\n",
    "df_encoded.to_csv(output_path, index=False)\n",
    "print(f\"\\n✅ Dataset processado salvo em: {output_path}\")\n",
    "\n",
    "print(\"\\n🎯 PRÉ-PROCESSAMENTO CONCLUÍDO COM SUCESSO!\")\n",
    "print(\"   O dataset está pronto para uso em modelos preditivos.\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.13.2"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
