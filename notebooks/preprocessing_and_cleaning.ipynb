import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings('ignore')

# Configurações de visualização
plt.style.use('default')
sns.set_palette("husl")
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("Bibliotecas importadas com sucesso!")

# Importar módulo de filtragem
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))
from data_filtering import apply_business_filters

# Carregar dados filtrados
df = apply_business_filters('../assets/dados.csv', verbose=True)

print(f"\nDataset carregado com {df.shape[0]:,} registros e {df.shape[1]} colunas")
print(f"Período dos dados: {df['ID_Date'].min()} a {df['ID_Date'].max()}")

# Análise da estrutura dos dados
print("=== INFORMAÇÕES GERAIS DO DATASET ===")
print(df.info())

print("\n=== TIPOS DE DADOS ===")
tipos_dados = df.dtypes.value_counts()
print(tipos_dados)

print("\n=== ESTATÍSTICAS DESCRITIVAS - COLUNAS NUMÉRICAS ===")
colunas_numericas = df.select_dtypes(include=[np.number]).columns.tolist()
print(f"Colunas numéricas identificadas: {len(colunas_numericas)}")
print(colunas_numericas)

# Estatísticas das principais colunas numéricas de interesse
colunas_interesse = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
colunas_disponiveis = [col for col in colunas_interesse if col in df.columns]
print(f"\nEstatísticas das colunas de interesse:")
print(df[colunas_disponiveis].describe())

# Análise das colunas categóricas
print("=== COLUNAS CATEGÓRICAS ===")
colunas_categoricas = df.select_dtypes(include=['object']).columns.tolist()
print(f"Colunas categóricas identificadas: {len(colunas_categoricas)}")

# Principais colunas categóricas de interesse
colunas_cat_interesse = [
    'Dim_Lojas.Tipo_PDV', 'Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI',
    'Dim_Cliente.Sexo', 'Dim_Cliente.Estado_Civil', 'Dim_Cliente.Uf_Cliente',
    'Dim_Produtos.Grupo_Produto', 'Dim_Produtos.Sub_Grupo', 'Dim_Produtos.Sexo'
]

for col in colunas_cat_interesse:
    if col in df.columns:
        valores_unicos = df[col].nunique()
        print(f"\n{col}: {valores_unicos} valores únicos")
        print(df[col].value_counts().head())

# Análise de valores ausentes
print("=== ANÁLISE DE VALORES AUSENTES ===")

# Contagem de valores nulos por coluna
missing_data = df.isnull().sum()
missing_percent = (missing_data / len(df)) * 100

# Criar DataFrame com informações de missing values
missing_df = pd.DataFrame({
    'Coluna': missing_data.index,
    'Valores_Ausentes': missing_data.values,
    'Percentual': missing_percent.values
})

# Filtrar apenas colunas com valores ausentes
missing_df = missing_df[missing_df['Valores_Ausentes'] > 0].sort_values('Valores_Ausentes', ascending=False)

print(f"Total de colunas com valores ausentes: {len(missing_df)}")
print("\nColunas com valores ausentes:")
print(missing_df.to_string(index=False))

# Visualização dos valores ausentes
if len(missing_df) > 0:
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 2, 1)
    missing_df.head(10).plot(x='Coluna', y='Valores_Ausentes', kind='bar', ax=plt.gca())
    plt.title('Top 10 Colunas com Valores Ausentes')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    plt.subplot(1, 2, 2)
    missing_df.head(10).plot(x='Coluna', y='Percentual', kind='bar', ax=plt.gca())
    plt.title('Percentual de Valores Ausentes')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    plt.show()
else:
    print("\n✅ Não há valores ausentes no dataset!")

# Estratégias de tratamento de valores ausentes
print("=== ESTRATÉGIAS DE TRATAMENTO DE VALORES AUSENTES ===")

# Criar cópia do dataframe para tratamento
df_treated = df.copy()
acoes_realizadas = []

# Verificar se há valores ausentes para tratar
if len(missing_df) > 0:
    for _, row in missing_df.iterrows():
        coluna = row['Coluna']
        valores_ausentes = row['Valores_Ausentes']
        percentual = row['Percentual']
        
        print(f"\nTratando coluna: {coluna} ({valores_ausentes} valores ausentes, {percentual:.2f}%)")
        
        # Estratégia baseada no tipo de dados e percentual de missing
        if percentual > 50:
            # Remover colunas com mais de 50% de valores ausentes
            df_treated = df_treated.drop(columns=[coluna])
            acao = f"Remoção da coluna (>50% ausentes)"
            print(f"  → {acao}")
            acoes_realizadas.append(f"{coluna}: {acao}")
            
        elif df_treated[coluna].dtype in ['object']:
            # Para categóricas: imputar com moda
            moda = df_treated[coluna].mode()[0] if not df_treated[coluna].mode().empty else 'Não informado'
            df_treated[coluna].fillna(moda, inplace=True)
            acao = f"Imputação com moda: '{moda}'"
            print(f"  → {acao}")
            acoes_realizadas.append(f"{coluna}: {acao}")
            
        elif df_treated[coluna].dtype in ['int64', 'float64']:
            # Para numéricas: imputar com mediana (mais robusta a outliers)
            mediana = df_treated[coluna].median()
            df_treated[coluna].fillna(mediana, inplace=True)
            acao = f"Imputação com mediana: {mediana}"
            print(f"  → {acao}")
            acoes_realizadas.append(f"{coluna}: {acao}")
            
        else:
            # Para outros tipos: remover registros
            df_treated = df_treated.dropna(subset=[coluna])
            acao = f"Remoção de registros com valores ausentes"
            print(f"  → {acao}")
            acoes_realizadas.append(f"{coluna}: {acao}")

    print(f"\n=== RESULTADO DO TRATAMENTO ===")
    print(f"Registros antes: {len(df):,}")
    print(f"Registros depois: {len(df_treated):,}")
    print(f"Colunas antes: {df.shape[1]}")
    print(f"Colunas depois: {df_treated.shape[1]}")
    
    # Verificar se ainda há valores ausentes
    remaining_missing = df_treated.isnull().sum().sum()
    print(f"Valores ausentes restantes: {remaining_missing}")
    
else:
    print("✅ Não há valores ausentes para tratar!")
    acoes_realizadas.append("Nenhuma ação necessária - dataset sem valores ausentes")

# Atualizar dataframe principal
df = df_treated.copy()

# Função para detectar outliers usando IQR
def detect_outliers_iqr(data, column):
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

# Função para detectar outliers usando Z-score
def detect_outliers_zscore(data, column, threshold=3):
    z_scores = np.abs(stats.zscore(data[column]))
    outliers = data[z_scores > threshold]
    return outliers, threshold

print("=== DETECÇÃO DE OUTLIERS ===")

# Colunas numéricas para análise de outliers
colunas_outliers = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
colunas_outliers = [col for col in colunas_outliers if col in df.columns]

outliers_info = []

for coluna in colunas_outliers:
    print(f"\n--- Análise de outliers: {coluna} ---")
    
    # Estatísticas básicas
    print(f"Estatísticas básicas:")
    print(f"  Média: {df[coluna].mean():.2f}")
    print(f"  Mediana: {df[coluna].median():.2f}")
    print(f"  Desvio padrão: {df[coluna].std():.2f}")
    print(f"  Min: {df[coluna].min():.2f}")
    print(f"  Max: {df[coluna].max():.2f}")
    
    # Detecção por IQR
    outliers_iqr, lower_iqr, upper_iqr = detect_outliers_iqr(df, coluna)
    print(f"\nOutliers por IQR:")
    print(f"  Limites: [{lower_iqr:.2f}, {upper_iqr:.2f}]")
    print(f"  Outliers encontrados: {len(outliers_iqr)} ({len(outliers_iqr)/len(df)*100:.2f}%)")
    
    # Detecção por Z-score
    outliers_zscore, threshold = detect_outliers_zscore(df, coluna)
    print(f"\nOutliers por Z-score (threshold={threshold}):")
    print(f"  Outliers encontrados: {len(outliers_zscore)} ({len(outliers_zscore)/len(df)*100:.2f}%)")
    
    # Armazenar informações
    outliers_info.append({
        'coluna': coluna,
        'outliers_iqr': len(outliers_iqr),
        'outliers_zscore': len(outliers_zscore),
        'lower_iqr': lower_iqr,
        'upper_iqr': upper_iqr,
        'percentual_iqr': len(outliers_iqr)/len(df)*100
    })

# Resumo dos outliers
outliers_summary = pd.DataFrame(outliers_info)
print("\n=== RESUMO DE OUTLIERS ===")
print(outliers_summary.to_string(index=False))

# Visualização de outliers
print("=== VISUALIZAÇÃO DE OUTLIERS ===")

# Criar subplots para boxplots
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.ravel()

for i, coluna in enumerate(colunas_outliers):
    if i < len(axes):
        # Boxplot
        df.boxplot(column=coluna, ax=axes[i])
        axes[i].set_title(f'Boxplot - {coluna}')
        axes[i].grid(True, alpha=0.3)

# Remover subplots vazios
for i in range(len(colunas_outliers), len(axes)):
    fig.delaxes(axes[i])

plt.tight_layout()
plt.show()

# Histogramas para visualizar distribuições
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.ravel()

for i, coluna in enumerate(colunas_outliers):
    if i < len(axes):
        axes[i].hist(df[coluna], bins=50, alpha=0.7, edgecolor='black')
        axes[i].set_title(f'Distribuição - {coluna}')
        axes[i].set_xlabel(coluna)
        axes[i].set_ylabel('Frequência')
        axes[i].grid(True, alpha=0.3)

# Remover subplots vazios
for i in range(len(colunas_outliers), len(axes)):
    fig.delaxes(axes[i])

plt.tight_layout()
plt.show()

# Tratamento de outliers
print("=== TRATAMENTO DE OUTLIERS ===")

df_outlier_treated = df.copy()
acoes_outliers = []

for info in outliers_info:
    coluna = info['coluna']
    percentual_iqr = info['percentual_iqr']
    lower_iqr = info['lower_iqr']
    upper_iqr = info['upper_iqr']
    
    print(f"\n--- Tratamento de outliers: {coluna} ---")
    print(f"Outliers detectados: {info['outliers_iqr']} ({percentual_iqr:.2f}%)")
    
    # Estratégia baseada no percentual de outliers e contexto de negócio
    if percentual_iqr > 10:
        # Se mais de 10% são outliers, usar cap truncation (winsorização)
        print(f"  → Aplicando cap truncation (>10% outliers)")
        df_outlier_treated[coluna] = np.where(
            df_outlier_treated[coluna] < lower_iqr, lower_iqr, df_outlier_treated[coluna]
        )
        df_outlier_treated[coluna] = np.where(
            df_outlier_treated[coluna] > upper_iqr, upper_iqr, df_outlier_treated[coluna]
        )
        acao = f"Cap truncation nos limites [{lower_iqr:.2f}, {upper_iqr:.2f}]"
        
    elif percentual_iqr > 5:
        # Entre 5-10%, substituir por percentis 5 e 95
        print(f"  → Substituindo por percentis 5% e 95%")
        p5 = df_outlier_treated[coluna].quantile(0.05)
        p95 = df_outlier_treated[coluna].quantile(0.95)
        df_outlier_treated[coluna] = np.where(
            df_outlier_treated[coluna] < p5, p5, df_outlier_treated[coluna]
        )
        df_outlier_treated[coluna] = np.where(
            df_outlier_treated[coluna] > p95, p95, df_outlier_treated[coluna]
        )
        acao = f"Substituição por percentis 5% ({p5:.2f}) e 95% ({p95:.2f})"
        
    elif percentual_iqr > 1:
        # Entre 1-5%, remover outliers extremos
        print(f"  → Removendo outliers extremos")
        outliers_mask = (df_outlier_treated[coluna] < lower_iqr) | (df_outlier_treated[coluna] > upper_iqr)
        df_outlier_treated = df_outlier_treated[~outliers_mask]
        acao = f"Remoção de {outliers_mask.sum()} registros outliers"
        
    else:
        # Menos de 1%, manter como está
        print(f"  → Mantendo outliers (<1% dos dados)")
        acao = "Nenhuma ação - outliers mantidos (<1%)"
    
    acoes_outliers.append(f"{coluna}: {acao}")

print(f"\n=== RESULTADO DO TRATAMENTO DE OUTLIERS ===")
print(f"Registros antes: {len(df):,}")
print(f"Registros depois: {len(df_outlier_treated):,}")
registros_removidos = len(df) - len(df_outlier_treated)
print(f"Registros removidos: {registros_removidos:,} ({registros_removidos/len(df)*100:.2f}%)")

# Atualizar dataframe principal
df = df_outlier_treated.copy()

print("=== NORMALIZAÇÃO/PADRONIZAÇÃO ===")

# Colunas para normalização (excluindo IDs e colunas calculadas)
colunas_para_normalizar = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
colunas_para_normalizar = [col for col in colunas_para_normalizar if col in df.columns]

# Criar cópia para transformações
df_normalized = df.copy()
acoes_normalizacao = []

# Aplicar Min-Max Normalization (0-1)
print("\n--- Aplicando Normalização Min-Max (0-1) ---")
scaler_minmax = MinMaxScaler()

for coluna in colunas_para_normalizar:
    print(f"Normalizando: {coluna}")
    
    # Valores originais
    original_min = df[coluna].min()
    original_max = df[coluna].max()
    
    # Aplicar normalização
    df_normalized[f'{coluna}_normalized'] = scaler_minmax.fit_transform(df[[coluna]])
    
    # Verificar resultado
    new_min = df_normalized[f'{coluna}_normalized'].min()
    new_max = df_normalized[f'{coluna}_normalized'].max()
    
    print(f"  Original: [{original_min:.2f}, {original_max:.2f}] → Normalizado: [{new_min:.2f}, {new_max:.2f}]")
    acoes_normalizacao.append(f"{coluna} → normalização Min-Max (0-1)")

# Aplicar Padronização Z-score (média=0, desvio=1)
print("\n--- Aplicando Padronização Z-score (μ=0, σ=1) ---")
scaler_standard = StandardScaler()

for coluna in colunas_para_normalizar:
    print(f"Padronizando: {coluna}")
    
    # Valores originais
    original_mean = df[coluna].mean()
    original_std = df[coluna].std()
    
    # Aplicar padronização
    df_normalized[f'{coluna}_standardized'] = scaler_standard.fit_transform(df[[coluna]])
    
    # Verificar resultado
    new_mean = df_normalized[f'{coluna}_standardized'].mean()
    new_std = df_normalized[f'{coluna}_standardized'].std()
    
    print(f"  Original: μ={original_mean:.2f}, σ={original_std:.2f} → Padronizado: μ={new_mean:.2f}, σ={new_std:.2f}")
    acoes_normalizacao.append(f"{coluna} → padronização Z-score (μ=0, σ=1)")

print(f"\n✅ Transformações de escala aplicadas em {len(colunas_para_normalizar)} colunas")

print("=== CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS ===")

# Criar cópia para codificação
df_encoded = df_normalized.copy()
acoes_codificacao = []

# Definir variáveis categóricas nominais (One-Hot Encoding)
variaveis_nominais = [
    'Dim_Lojas.Tipo_PDV',
    'Dim_Lojas.CANAL_VENDA', 
    'Dim_Lojas.REGIAO_CHILLI',
    'Dim_Cliente.Uf_Cliente',
    'Dim_Produtos.Grupo_Produto',
    'Dim_Produtos.Sub_Grupo',
    'Dim_Produtos.Sexo'
]

# Definir variáveis categóricas ordinais (Label Encoding)
variaveis_ordinais = [
    'Dim_Cliente.Sexo',  # M, F
    'Dim_Cliente.Estado_Civil'  # Solteiro, Casado, etc.
]

# Filtrar apenas colunas existentes
variaveis_nominais = [col for col in variaveis_nominais if col in df_encoded.columns]
variaveis_ordinais = [col for col in variaveis_ordinais if col in df_encoded.columns]

print(f"\nVariáveis nominais para One-Hot Encoding: {len(variaveis_nominais)}")
print(f"Variáveis ordinais para Label Encoding: {len(variaveis_ordinais)}")

# Aplicar One-Hot Encoding para variáveis nominais
print("\n--- One-Hot Encoding (Variáveis Nominais) ---")
for coluna in variaveis_nominais:
    print(f"\nProcessando: {coluna}")
    
    # Verificar número de categorias únicas
    categorias_unicas = df_encoded[coluna].nunique()
    print(f"  Categorias únicas: {categorias_unicas}")
    
    if categorias_unicas > 20:
        # Para muitas categorias, manter apenas as top 10 mais frequentes
        print(f"  → Muitas categorias ({categorias_unicas}), mantendo top 10")
        top_categories = df_encoded[coluna].value_counts().head(10).index.tolist()
        df_encoded[coluna] = df_encoded[coluna].apply(
            lambda x: x if x in top_categories else 'Outros'
        )
        categorias_unicas = df_encoded[coluna].nunique()
        print(f"  → Categorias após agrupamento: {categorias_unicas}")
    
    # Aplicar One-Hot Encoding
    dummies = pd.get_dummies(df_encoded[coluna], prefix=coluna, drop_first=True)
    df_encoded = pd.concat([df_encoded, dummies], axis=1)
    
    print(f"  → Criadas {len(dummies.columns)} colunas dummy")
    acoes_codificacao.append(f"{coluna} → One-Hot Encoding ({len(dummies.columns)} colunas criadas)")

# Aplicar Label Encoding para variáveis ordinais
print("\n--- Label Encoding (Variáveis Ordinais) ---")
label_encoders = {}

for coluna in variaveis_ordinais:
    print(f"\nProcessando: {coluna}")
    
    # Verificar valores únicos
    valores_unicos = df_encoded[coluna].unique()
    print(f"  Valores únicos: {valores_unicos}")
    
    # Aplicar Label Encoding
    le = LabelEncoder()
    df_encoded[f'{coluna}_encoded'] = le.fit_transform(df_encoded[coluna].astype(str))
    
    # Armazenar encoder para referência
    label_encoders[coluna] = le
    
    # Mostrar mapeamento
    mapping = dict(zip(le.classes_, le.transform(le.classes_)))
    print(f"  → Mapeamento: {mapping}")
    acoes_codificacao.append(f"{coluna} → Label Encoding (mapeamento: {mapping})")

print(f"\n=== RESULTADO DA CODIFICAÇÃO ===")
print(f"Colunas antes: {df_normalized.shape[1]}")
print(f"Colunas depois: {df_encoded.shape[1]}")
novas_colunas = df_encoded.shape[1] - df_normalized.shape[1]
print(f"Novas colunas criadas: {novas_colunas}")

print("=== DOCUMENTAÇÃO COMPLETA DAS TRANSFORMAÇÕES ===")

print("\n1. TRATAMENTO DE VALORES AUSENTES:")
for acao in acoes_realizadas:
    print(f"   • {acao}")

print("\n2. TRATAMENTO DE OUTLIERS:")
for acao in acoes_outliers:
    print(f"   • {acao}")

print("\n3. NORMALIZAÇÃO/PADRONIZAÇÃO:")
for acao in acoes_normalizacao:
    print(f"   • {acao}")

print("\n4. CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS:")
for acao in acoes_codificacao:
    print(f"   • {acao}")

print("\n=== RESUMO FINAL DO DATASET ===")
print(f"Dataset original: {40291:,} registros, {61} colunas")
print(f"Dataset filtrado: {len(df):,} registros, {df.shape[1]} colunas")
print(f"Dataset final processado: {len(df_encoded):,} registros, {df_encoded.shape[1]} colunas")

# Verificações finais
print("\n=== VERIFICAÇÕES FINAIS ===")
print(f"✅ Valores ausentes restantes: {df_encoded.isnull().sum().sum()}")
print(f"✅ Tipos de dados:")
tipos_finais = df_encoded.dtypes.value_counts()
for tipo, count in tipos_finais.items():
    print(f"   • {tipo}: {count} colunas")

# Salvar dataset processado
output_path = '../assets/dados_processados.csv'
df_encoded.to_csv(output_path, index=False)
print(f"\n✅ Dataset processado salvo em: {output_path}")

print("\n🎯 PRÉ-PROCESSAMENTO CONCLUÍDO COM SUCESSO!")
print("   O dataset está pronto para uso em modelos preditivos.")